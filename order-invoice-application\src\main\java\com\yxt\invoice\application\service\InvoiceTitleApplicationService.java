package com.yxt.invoice.application.service;

import com.yxt.invoice.domain.model.InvoiceTitle;
import com.yxt.invoice.domain.repository.InvoiceTitleRepository;
import com.yxt.invoice.sdk.dto.InvoiceTitleDTO;
import com.yxt.invoice.sdk.dto.req.*;
import com.yxt.lang.dto.api.PageDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 发票抬头应用服务
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-06
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class InvoiceTitleApplicationService {

    @Resource
    private InvoiceTitleRepository invoiceTitleRepository;

    /**
     * 新增发票抬头
     */
    public String createInvoiceTitle(CreateInvoiceTitleReqDto request) {
        log.info("新增发票抬头，用户ID：{}，购方名称：{}", request.getUserId(), request.getBuyerName());

        // 创建发票抬头领域对象
        InvoiceTitle invoiceTitle = InvoiceTitle.create(
                request.getUserId(),
                request.getInvoiceType(),
                request.getBuyerPartyType(),
                request.getBuyerName(),
                request.getBuyerTin(),
                request.getOperatorUserId()
        );

        // 设置详细信息
        invoiceTitle.setBuyerDetails(
                request.getBuyerPhone(),
                request.getBuyerAddress(),
                request.getBuyerBank(),
                request.getBuyerBankAccount(),
                request.getBuyerEmail(),
                request.getBuyerMobile()
        );

        // 验证业务规则
        invoiceTitle.validateSpecialInvoiceFields();
        invoiceTitle.validateOrganizationFields();

        // 保存到仓储
        InvoiceTitle savedInvoiceTitle = invoiceTitleRepository.save(invoiceTitle);

        log.info("新增发票抬头成功，抬头单号：{}", savedInvoiceTitle.getInvoiceTitleNo());
        return savedInvoiceTitle.getInvoiceTitleNo();
    }

    /**
     * 编辑发票抬头
     */
    public void updateInvoiceTitle(UpdateInvoiceTitleReqDto request) {
        log.info("编辑发票抬头，抬头单号：{}", request.getInvoiceTitleNo());

        // 查询现有发票抬头
        InvoiceTitle invoiceTitle = invoiceTitleRepository.findByInvoiceTitleNo(request.getInvoiceTitleNo());
        if (invoiceTitle == null) {
            throw new IllegalArgumentException("发票抬头不存在，抬头单号：" + request.getInvoiceTitleNo());
        }

        if (!invoiceTitle.isValid()) {
            throw new IllegalArgumentException("发票抬头已删除，不能编辑，抬头单号：" + request.getInvoiceTitleNo());
        }

        // 更新基本信息
        invoiceTitle.update(
                request.getInvoiceType(),
                request.getBuyerPartyType(),
                request.getBuyerName(),
                request.getBuyerTin(),
                request.getOperatorUserId()
        );

        // 更新详细信息
        invoiceTitle.setBuyerDetails(
                request.getBuyerPhone(),
                request.getBuyerAddress(),
                request.getBuyerBank(),
                request.getBuyerBankAccount(),
                request.getBuyerEmail(),
                request.getBuyerMobile()
        );

        // 验证业务规则
        invoiceTitle.validateSpecialInvoiceFields();
        invoiceTitle.validateOrganizationFields();

        // 保存更新
        invoiceTitleRepository.update(invoiceTitle);

        log.info("编辑发票抬头成功，抬头单号：{}", request.getInvoiceTitleNo());
    }

    /**
     * 删除发票抬头
     */
    public void deleteInvoiceTitle(DeleteInvoiceTitleReqDto request) {
        log.info("删除发票抬头，抬头单号：{}", request.getInvoiceTitleNo());

        // 查询现有发票抬头
        InvoiceTitle invoiceTitle = invoiceTitleRepository.findByInvoiceTitleNo(request.getInvoiceTitleNo());
        if (invoiceTitle == null) {
            throw new IllegalArgumentException("发票抬头不存在，抬头单号：" + request.getInvoiceTitleNo());
        }

        if (!invoiceTitle.isValid()) {
            throw new IllegalArgumentException("发票抬头已删除，抬头单号：" + request.getInvoiceTitleNo());
        }

        // 执行逻辑删除
        invoiceTitle.delete(request.getOperatorUserId());
        invoiceTitleRepository.update(invoiceTitle);

        log.info("删除发票抬头成功，抬头单号：{}", request.getInvoiceTitleNo());
    }

    /**
     * 查询发票抬头列表
     */
    @Transactional(readOnly = true)
    public PageDTO<InvoiceTitleDTO> getInvoiceTitleList(InvoiceTitleListReqDto request) {
        log.info("查询发票抬头列表，用户ID：{}", request.getUserId());

        // 查询有效的发票抬头列表
        List<InvoiceTitle> invoiceTitleList = invoiceTitleRepository.findValidByUserId(request.getUserId());

        // 转换为DTO
        List<InvoiceTitleDTO> dtoList = invoiceTitleList.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());

        // 简单分页处理（实际项目中应该在数据库层面分页）
        int total = dtoList.size();
        int startIndex = (request.getCurrentPage() - 1) * request.getPageSize();
        int endIndex = Math.min(startIndex + request.getPageSize(), total);

        List<InvoiceTitleDTO> pagedList = dtoList.subList(startIndex, endIndex);

        PageDTO<InvoiceTitleDTO> pageResult = new PageDTO<>(request.getCurrentPage(), request.getPageSize());
        pageResult.setData(pagedList);
        pageResult.setTotalCount((long) total);
        pageResult.setTotalPage((int) Math.ceil((double) total / request.getPageSize()));

        log.info("查询发票抬头列表成功，用户ID：{}，总数：{}", request.getUserId(), total);
        return pageResult;
    }

    /**
     * 查询发票抬头详情
     */
    @Transactional(readOnly = true)
    public InvoiceTitleDTO getInvoiceTitleDetail(InvoiceTitleDetailReqDto request) {
        log.info("查询发票抬头详情，抬头单号：{}", request.getInvoiceTitleNo());

        InvoiceTitle invoiceTitle = invoiceTitleRepository.findByInvoiceTitleNo(request.getInvoiceTitleNo());
        if (invoiceTitle == null) {
            throw new IllegalArgumentException("发票抬头不存在，抬头单号：" + request.getInvoiceTitleNo());
        }

        InvoiceTitleDTO result = convertToDTO(invoiceTitle);
        log.info("查询发票抬头详情成功，抬头单号：{}", request.getInvoiceTitleNo());
        return result;
    }

    /**
     * 转换为DTO
     */
    private InvoiceTitleDTO convertToDTO(InvoiceTitle invoiceTitle) {
        InvoiceTitleDTO dto = new InvoiceTitleDTO();
        dto.setId(invoiceTitle.getId());
        dto.setInvoiceTitleNo(invoiceTitle.getInvoiceTitleNo());
        dto.setUserId(invoiceTitle.getUserId());
        dto.setInvoiceType(invoiceTitle.getInvoiceType());
        dto.setBuyerPartyType(invoiceTitle.getBuyerPartyType());
        dto.setBuyerName(invoiceTitle.getBuyerName());
        dto.setBuyerTin(invoiceTitle.getBuyerTin());
        dto.setBuyerPhone(invoiceTitle.getBuyerPhone());
        dto.setBuyerAddress(invoiceTitle.getBuyerAddress());
        dto.setBuyerBank(invoiceTitle.getBuyerBank());
        dto.setBuyerBankAccount(invoiceTitle.getBuyerBankAccount());
        dto.setBuyerEmail(invoiceTitle.getBuyerEmail());
        dto.setBuyerMobile(invoiceTitle.getBuyerMobile());
        dto.setIsValid(invoiceTitle.getIsValid());
        dto.setCreatedBy(invoiceTitle.getCreatedBy());
        dto.setUpdatedBy(invoiceTitle.getUpdatedBy());
        dto.setSysCreateTime(invoiceTitle.getSysCreateTime());
        dto.setSysUpdateTime(invoiceTitle.getSysUpdateTime());
        dto.setVersion(invoiceTitle.getVersion());
        return dto;
    }
}
