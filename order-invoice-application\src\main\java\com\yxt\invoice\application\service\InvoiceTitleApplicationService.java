package com.yxt.invoice.application.service;

import com.yxt.invoice.domain.command.title.CreateInvoiceTitleCommand;
import com.yxt.invoice.domain.command.title.DeleteInvoiceTitleCommand;
import com.yxt.invoice.domain.command.title.InvoiceTitleDetailCommand;
import com.yxt.invoice.domain.command.title.InvoiceTitleListCommand;
import com.yxt.invoice.domain.command.title.UpdateInvoiceTitleCommand;
import com.yxt.invoice.domain.model.InvoiceTitle;
import com.yxt.invoice.domain.repository.InvoiceTitleRepository;
import com.yxt.lang.dto.api.PageDTO;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 发票抬头应用服务
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-06
 */
@Service
@Slf4j
public class InvoiceTitleApplicationService {

  @Resource
  private InvoiceTitleRepository invoiceTitleRepository;

  /**
   * 新增发票抬头
   */
  public String createInvoiceTitle(CreateInvoiceTitleCommand command) {
    // 创建发票抬头领域对象
    InvoiceTitle invoiceTitle = InvoiceTitle.create(command.getUserId(), command.getInvoiceType(),
        command.getBuyerPartyType(), command.getBuyerName(), command.getBuyerTin(),
        command.getOperatorUserId());

    // 设置详细信息
    invoiceTitle.setBuyerDetails(command.getBuyerPhone(), command.getBuyerAddress(),
        command.getBuyerBank(), command.getBuyerBankAccount(), command.getBuyerEmail(),
        command.getBuyerMobile());

    // 验证业务规则
    invoiceTitle.validateSpecialInvoiceFields();
    invoiceTitle.validateOrganizationFields();

    // 保存到仓储
    InvoiceTitle savedInvoiceTitle = invoiceTitleRepository.save(invoiceTitle);

    return savedInvoiceTitle.getInvoiceTitleNo();
  }

  /**
   * 编辑发票抬头
   */
  public void updateInvoiceTitle(UpdateInvoiceTitleCommand command) {
    // 查询现有发票抬头
    InvoiceTitle invoiceTitle = invoiceTitleRepository.findByInvoiceTitleNo(
        command.getInvoiceTitleNo());
    if (invoiceTitle == null) {
      throw new IllegalArgumentException("发票抬头不存在，抬头单号：" + command.getInvoiceTitleNo());
    }

    if (!invoiceTitle.isValid()) {
      throw new IllegalArgumentException(
          "发票抬头已删除，不能编辑，抬头单号：" + command.getInvoiceTitleNo());
    }

    // 更新基本信息
    invoiceTitle.update(command.getInvoiceType(), command.getBuyerPartyType(),
        command.getBuyerName(), command.getBuyerTin(), command.getOperatorUserId());

    // 更新详细信息
    invoiceTitle.setBuyerDetails(command.getBuyerPhone(), command.getBuyerAddress(),
        command.getBuyerBank(), command.getBuyerBankAccount(), command.getBuyerEmail(),
        command.getBuyerMobile());

    // 验证业务规则
    invoiceTitle.validateSpecialInvoiceFields();
    invoiceTitle.validateOrganizationFields();

    // 保存更新
    invoiceTitleRepository.update(invoiceTitle);
  }

  /**
   * 删除发票抬头
   */
  public void deleteInvoiceTitle(DeleteInvoiceTitleCommand command) {
    // 查询现有发票抬头
    InvoiceTitle invoiceTitle = invoiceTitleRepository.findByInvoiceTitleNo(
        command.getInvoiceTitleNo());
    if (invoiceTitle == null) {
      throw new IllegalArgumentException("发票抬头不存在，抬头单号：" + command.getInvoiceTitleNo());
    }

    if (!invoiceTitle.isValid()) {
      throw new IllegalArgumentException("发票抬头已删除，抬头单号：" + command.getInvoiceTitleNo());
    }

    // 执行逻辑删除
    invoiceTitle.delete(command.getOperatorUserId());
    invoiceTitleRepository.update(invoiceTitle);
  }

  /**
   * 查询发票抬头列表
   */
  public PageDTO<InvoiceTitle> getInvoiceTitleList(InvoiceTitleListCommand command) {
    // 查询有效的发票抬头列表
    return invoiceTitleRepository.pageInvoiceTitle(command.getUserId(),command.getCurrentPage(),command.getPageSize());

  }

  /**
   * 查询发票抬头详情
   */
  public InvoiceTitle getInvoiceTitleDetail(InvoiceTitleDetailCommand command) {
    return invoiceTitleRepository.findByInvoiceTitleNo(command.getInvoiceTitleNo());
  }


}
