package com.yxt.invoice.interfaces.converter;

import com.yxt.invoice.domain.command.title.CreateInvoiceTitleCommand;
import com.yxt.invoice.domain.command.title.DeleteInvoiceTitleCommand;
import com.yxt.invoice.domain.command.title.InvoiceTitleDetailCommand;
import com.yxt.invoice.domain.command.title.InvoiceTitleListCommand;
import com.yxt.invoice.domain.command.title.UpdateInvoiceTitleCommand;
import com.yxt.invoice.domain.model.InvoiceTitle;
import com.yxt.invoice.sdk.dto.InvoiceTitleDTO;
import com.yxt.invoice.sdk.dto.req.title.CreateInvoiceTitleReqDto;
import com.yxt.invoice.sdk.dto.req.title.DeleteInvoiceTitleReqDto;
import com.yxt.invoice.sdk.dto.req.title.InvoiceTitleDetailReqDto;
import com.yxt.invoice.sdk.dto.req.title.InvoiceTitleListReqDto;
import com.yxt.invoice.sdk.dto.req.title.UpdateInvoiceTitleReqDto;
import lombok.extern.slf4j.Slf4j;

/**
 * 发票请求转换器 负责将SDK请求对象转换为Domain Command或Application Query
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@Slf4j
public class InvoiceTitleConverter {

  /**
   * 转换新增发票抬头请求为命令对象
   */
  public static CreateInvoiceTitleCommand convert(CreateInvoiceTitleReqDto request) {
    CreateInvoiceTitleCommand command = new CreateInvoiceTitleCommand();
    command.setUserId(request.getUserId());
    command.setInvoiceType(request.getInvoiceType());
    command.setBuyerPartyType(request.getBuyerPartyType());
    command.setBuyerName(request.getBuyerName());
    command.setBuyerTin(request.getBuyerTin());
    command.setBuyerPhone(request.getBuyerPhone());
    command.setBuyerAddress(request.getBuyerAddress());
    command.setBuyerBank(request.getBuyerBank());
    command.setBuyerBankAccount(request.getBuyerBankAccount());
    command.setBuyerEmail(request.getBuyerEmail());
    command.setBuyerMobile(request.getBuyerMobile());
    command.setOperatorUserId(request.getOperatorUserId());

    return command;
  }

  /**
   * 转换编辑发票抬头请求为命令对象
   */
  public static UpdateInvoiceTitleCommand convert(UpdateInvoiceTitleReqDto request) {
    UpdateInvoiceTitleCommand command = new UpdateInvoiceTitleCommand();
    command.setInvoiceTitleNo(request.getInvoiceTitleNo());
    command.setInvoiceType(request.getInvoiceType());
    command.setBuyerPartyType(request.getBuyerPartyType());
    command.setBuyerName(request.getBuyerName());
    command.setBuyerTin(request.getBuyerTin());
    command.setBuyerPhone(request.getBuyerPhone());
    command.setBuyerAddress(request.getBuyerAddress());
    command.setBuyerBank(request.getBuyerBank());
    command.setBuyerBankAccount(request.getBuyerBankAccount());
    command.setBuyerEmail(request.getBuyerEmail());
    command.setBuyerMobile(request.getBuyerMobile());
    command.setOperatorUserId(request.getOperatorUserId());

    return command;
  }

  /**
   * 转换删除发票抬头请求为命令对象
   */
  public static DeleteInvoiceTitleCommand convert(DeleteInvoiceTitleReqDto request) {
    DeleteInvoiceTitleCommand command = new DeleteInvoiceTitleCommand();
    command.setInvoiceTitleNo(request.getInvoiceTitleNo());
    command.setOperatorUserId(request.getOperatorUserId());

    return command;
  }

  /**
   * 转换发票抬头列表请求为命令对象
   */
  public static InvoiceTitleListCommand convert(InvoiceTitleListReqDto request) {
    InvoiceTitleListCommand command = new InvoiceTitleListCommand();
    command.setUserId(request.getUserId());
    command.setCurrentPage(request.getCurrentPage());
    command.setPageSize(request.getPageSize());

    return command;
  }

  /**
   * 转换发票抬头详情请求为命令对象
   */
  public static InvoiceTitleDetailCommand convert(InvoiceTitleDetailReqDto request) {
    InvoiceTitleDetailCommand command = new InvoiceTitleDetailCommand();
    command.setInvoiceTitleNo(request.getInvoiceTitleNo());

    return command;
  }



  /**
   * 转换为DTO
   */
  public static InvoiceTitleDTO convertToDTO(InvoiceTitle invoiceTitle) {
    InvoiceTitleDTO dto = new InvoiceTitleDTO();
    dto.setId(invoiceTitle.getId());
    dto.setInvoiceTitleNo(invoiceTitle.getInvoiceTitleNo());
    dto.setUserId(invoiceTitle.getUserId());
    dto.setInvoiceType(invoiceTitle.getInvoiceType());
    dto.setBuyerPartyType(invoiceTitle.getBuyerPartyType());
    dto.setBuyerName(invoiceTitle.getBuyerName());
    dto.setBuyerTin(invoiceTitle.getBuyerTin());
    dto.setBuyerPhone(invoiceTitle.getBuyerPhone());
    dto.setBuyerAddress(invoiceTitle.getBuyerAddress());
    dto.setBuyerBank(invoiceTitle.getBuyerBank());
    dto.setBuyerBankAccount(invoiceTitle.getBuyerBankAccount());
    dto.setBuyerEmail(invoiceTitle.getBuyerEmail());
    dto.setBuyerMobile(invoiceTitle.getBuyerMobile());
    dto.setIsValid(invoiceTitle.getIsValid());
    dto.setCreatedBy(invoiceTitle.getCreatedBy());
    dto.setUpdatedBy(invoiceTitle.getUpdatedBy());
    dto.setSysCreateTime(invoiceTitle.getSysCreateTime());
    dto.setSysUpdateTime(invoiceTitle.getSysUpdateTime());
    dto.setVersion(invoiceTitle.getVersion());
    return dto;
  }
}
