package com.yxt.invoice.sdk.dto.req.title;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 删除发票抬头请求
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-06
 */
@Data
public class DeleteInvoiceTitleReqDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 抬头内部单号
     */
    @ApiModelProperty(value = "抬头内部单号", required = true, example = "TITLE20250906001")
    @NotBlank(message = "抬头内部单号不能为空")
    @Size(max = 50, message = "抬头内部单号长度不能超过50个字符")
    private String invoiceTitleNo;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人", required = true, example = "admin")
    @NotBlank(message = "操作人不能为空")
    @Size(max = 50, message = "操作人长度不能超过50个字符")
    private String operatorUserId;
}
