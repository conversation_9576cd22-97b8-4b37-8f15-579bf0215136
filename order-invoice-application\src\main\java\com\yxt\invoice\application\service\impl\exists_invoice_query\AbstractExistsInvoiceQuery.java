package com.yxt.invoice.application.service.impl.exists_invoice_query;

import com.yxt.invoice.domain.command.OrderExistsInvoiceQueryCommand;
import com.yxt.invoice.domain.model.valueobject.ExistsOrderInvoice;
import com.yxt.order.types.invoice.enums.InvoiceBusinessTypeEnum;

public abstract class AbstractExistsInvoiceQuery {


  public abstract Boolean route(InvoiceBusinessTypeEnum businessTypeEnum);

  public abstract ExistsOrderInvoice build(OrderExistsInvoiceQueryCommand command);

}
