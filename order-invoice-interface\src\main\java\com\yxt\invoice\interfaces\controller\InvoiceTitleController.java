package com.yxt.invoice.interfaces.controller;

import com.yxt.invoice.application.service.InvoiceTitleApplicationService;
import com.yxt.invoice.domain.model.InvoiceTitle;
import com.yxt.invoice.interfaces.converter.InvoiceTitleConverter;
import com.yxt.invoice.sdk.api.InvoiceTitleApi;
import com.yxt.invoice.sdk.dto.InvoiceTitleDTO;
import com.yxt.invoice.sdk.dto.req.title.CreateInvoiceTitleReqDto;
import com.yxt.invoice.sdk.dto.req.title.DeleteInvoiceTitleReqDto;
import com.yxt.invoice.sdk.dto.req.title.InvoiceTitleDetailReqDto;
import com.yxt.invoice.sdk.dto.req.title.InvoiceTitleListReqDto;
import com.yxt.invoice.sdk.dto.req.title.UpdateInvoiceTitleReqDto;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 发票抬头控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-06
 */
@ApiOperation(value = "发票抬头管理接口")
@RestController
@RequestMapping("/api/1.0/invoice/title")
@Slf4j
public class InvoiceTitleController implements InvoiceTitleApi {

  @Resource
  private InvoiceTitleApplicationService invoiceTitleApplicationService;

  @Override
  public ResponseBase<String> create(CreateInvoiceTitleReqDto request) {
    String invoiceTitleNo = invoiceTitleApplicationService.createInvoiceTitle(
        InvoiceTitleConverter.convert(request));
    return ResponseBase.success(invoiceTitleNo);
  }

  @Override
  public ResponseBase<Void> update(UpdateInvoiceTitleReqDto request) {
    invoiceTitleApplicationService.updateInvoiceTitle(InvoiceTitleConverter.convert(request));
    return ResponseBase.success(null);
  }

  @Override
  public ResponseBase<Void> delete(DeleteInvoiceTitleReqDto request) {
    invoiceTitleApplicationService.deleteInvoiceTitle(InvoiceTitleConverter.convert(request));
    return ResponseBase.success(null);
  }

  @Override
  public ResponseBase<PageDTO<InvoiceTitleDTO>> list(InvoiceTitleListReqDto request) {
    PageDTO<InvoiceTitle> pageDTO = invoiceTitleApplicationService.getInvoiceTitleList(
        InvoiceTitleConverter.convert(request));

    List<InvoiceTitleDTO> collect = pageDTO.getData().stream()
        .map(InvoiceTitleConverter::convertToDTO).collect(Collectors.toList());

    PageDTO<InvoiceTitleDTO> data = new PageDTO<>();
    data.setTotalCount(pageDTO.getTotalCount());
    data.setTotalPage(pageDTO.getTotalPage());
    data.setData(collect);
    data.setCurrentPage(pageDTO.getCurrentPage());
    data.setPageSize(pageDTO.getPageSize());
    return ResponseBase.success(data);
  }

  @Override
  public ResponseBase<InvoiceTitleDTO> detail(InvoiceTitleDetailReqDto request) {
    InvoiceTitle result = invoiceTitleApplicationService.getInvoiceTitleDetail(
        InvoiceTitleConverter.convert(request));
    return ResponseBase.success(InvoiceTitleConverter.convertToDTO(result));
  }
}
