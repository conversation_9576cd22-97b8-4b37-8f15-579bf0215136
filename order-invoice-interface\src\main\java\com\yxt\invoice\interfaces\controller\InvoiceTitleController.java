package com.yxt.invoice.interfaces.controller;

import com.yxt.invoice.application.service.InvoiceTitleApplicationService;
import com.yxt.invoice.sdk.api.InvoiceTitleApi;
import com.yxt.invoice.sdk.dto.InvoiceTitleDTO;
import com.yxt.invoice.sdk.dto.req.title.CreateInvoiceTitleReqDto;
import com.yxt.invoice.sdk.dto.req.title.DeleteInvoiceTitleReqDto;
import com.yxt.invoice.sdk.dto.req.title.InvoiceTitleDetailReqDto;
import com.yxt.invoice.sdk.dto.req.title.InvoiceTitleListReqDto;
import com.yxt.invoice.sdk.dto.req.title.UpdateInvoiceTitleReqDto;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import io.swagger.annotations.ApiOperation;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 发票抬头控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-06
 */
@ApiOperation(value = "发票抬头管理接口")
@RestController
@RequestMapping("/api/1.0/invoice/title")
@Slf4j
public class InvoiceTitleController implements InvoiceTitleApi {

  @Resource
  private InvoiceTitleApplicationService invoiceTitleApplicationService;

  @Override
  public ResponseBase<String> createInvoiceTitle(CreateInvoiceTitleReqDto request) {
    String invoiceTitleNo = invoiceTitleApplicationService.createInvoiceTitle(request);
    return ResponseBase.success(invoiceTitleNo);
  }

  @Override
  public ResponseBase<Void> updateInvoiceTitle(UpdateInvoiceTitleReqDto request) {
    invoiceTitleApplicationService.updateInvoiceTitle(request);
    return ResponseBase.success(null);
  }

  @Override
  public ResponseBase<Void> deleteInvoiceTitle(DeleteInvoiceTitleReqDto request) {
    invoiceTitleApplicationService.deleteInvoiceTitle(request);
    return ResponseBase.success(null);
  }

  @Override
  public ResponseBase<PageDTO<InvoiceTitleDTO>> getInvoiceTitleList(
      InvoiceTitleListReqDto request) {
    PageDTO<InvoiceTitleDTO> result = invoiceTitleApplicationService.getInvoiceTitleList(request);
    return ResponseBase.success(result);
  }

  @Override
  public ResponseBase<InvoiceTitleDTO> getInvoiceTitleDetail(InvoiceTitleDetailReqDto request) {
    InvoiceTitleDTO result = invoiceTitleApplicationService.getInvoiceTitleDetail(request);
    return ResponseBase.success(result);
  }
}
