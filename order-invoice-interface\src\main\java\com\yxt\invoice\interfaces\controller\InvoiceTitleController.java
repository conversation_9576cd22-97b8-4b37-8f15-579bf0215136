package com.yxt.invoice.interfaces.controller;

import com.yxt.invoice.application.service.InvoiceTitleApplicationService;
import com.yxt.invoice.sdk.api.InvoiceTitleApi;
import com.yxt.invoice.sdk.dto.InvoiceTitleDTO;
import com.yxt.invoice.sdk.dto.req.*;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 发票抬头控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-06
 */
@ApiOperation(value = "发票抬头管理接口")
@RestController
@RequestMapping("/api/1.0/invoice/title")
@Slf4j
public class InvoiceTitleController implements InvoiceTitleApi {

    @Resource
    private InvoiceTitleApplicationService invoiceTitleApplicationService;

    @Override
    public ResponseBase<String> createInvoiceTitle(CreateInvoiceTitleReqDto request) {
        try {
            String invoiceTitleNo = invoiceTitleApplicationService.createInvoiceTitle(request);
            return ResponseBase.success(invoiceTitleNo);
        } catch (IllegalArgumentException e) {
            log.warn("新增发票抬头参数错误：{}", e.getMessage());
            return ResponseBase.failure("400", e.getMessage());
        } catch (Exception e) {
            log.error("新增发票抬头异常", e);
            return ResponseBase.failure("500", "系统异常");
        }
    }

    @Override
    public ResponseBase<Void> updateInvoiceTitle(UpdateInvoiceTitleReqDto request) {
        try {
            invoiceTitleApplicationService.updateInvoiceTitle(request);
            return ResponseBase.success(null);
        } catch (IllegalArgumentException e) {
            log.warn("编辑发票抬头参数错误：{}", e.getMessage());
            return ResponseBase.failure("400", e.getMessage());
        } catch (Exception e) {
            log.error("编辑发票抬头异常", e);
            return ResponseBase.failure("500", "系统异常");
        }
    }

    @Override
    public ResponseBase<Void> deleteInvoiceTitle(DeleteInvoiceTitleReqDto request) {
        try {
            invoiceTitleApplicationService.deleteInvoiceTitle(request);
            return ResponseBase.success(null);
        } catch (IllegalArgumentException e) {
            log.warn("删除发票抬头参数错误：{}", e.getMessage());
            return ResponseBase.failure("400", e.getMessage());
        } catch (Exception e) {
            log.error("删除发票抬头异常", e);
            return ResponseBase.failure("500", "系统异常");
        }
    }

    @Override
    public ResponseBase<PageDTO<InvoiceTitleDTO>> getInvoiceTitleList(InvoiceTitleListReqDto request) {
        try {
            PageDTO<InvoiceTitleDTO> result = invoiceTitleApplicationService.getInvoiceTitleList(request);
            return ResponseBase.success(result);
        } catch (Exception e) {
            log.error("查询发票抬头列表异常", e);
            return ResponseBase.failure("500", "系统异常");
        }
    }

    @Override
    public ResponseBase<InvoiceTitleDTO> getInvoiceTitleDetail(InvoiceTitleDetailReqDto request) {
        try {
            InvoiceTitleDTO result = invoiceTitleApplicationService.getInvoiceTitleDetail(request);
            return ResponseBase.success(result);
        } catch (IllegalArgumentException e) {
            log.warn("查询发票抬头详情参数错误：{}", e.getMessage());
            return ResponseBase.failure("400", e.getMessage());
        } catch (Exception e) {
            log.error("查询发票抬头详情异常", e);
            return ResponseBase.failure("500", "系统异常");
        }
    }
}
