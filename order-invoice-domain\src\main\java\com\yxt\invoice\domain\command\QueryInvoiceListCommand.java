package com.yxt.invoice.domain.command;

import com.yxt.lang.constants.ApiBizCodeEnum;
import com.yxt.order.types.invoice.enums.InvoiceRedBlueTypeEnum;
import com.yxt.order.types.invoice.enums.InvoiceStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 发票列表查询
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QueryInvoiceListCommand extends PageDateConditionCommand {

  /**
   * 分公司编码
   */
  @ApiModelProperty(value = "分公司编码")
  private List<String> companyCodeList;

  /**
   * 机构编码
   */
  @ApiModelProperty(value = "机构编码")
  private List<String> organizationCodeList;

  /**
   * 订单号
   */
  @ApiModelProperty(value = "平台订单号")
  private String thirdOrderNo;

  /**
   * pos销售单号
   */
  @ApiModelProperty(value = "POS销售单号")
  private String posNo;


  @ApiModelProperty(value = "开票申请开始时间") // 是申请时间,因为列表可以有开票失败和成功的数据
  private Date applyTimeStart;

  @ApiModelProperty(value = "开票申请开始时间")
  private Date applyTimeEnd;

  @ApiModelProperty("蓝票:TAX_INVOICE 红票:CREDIT_NOTE")
  private InvoiceRedBlueTypeEnum invoiceRedBlueType;

  /**
   * 发票状态
   */
  @ApiModelProperty(value = "发票状态 WAIT-待开票; PROGRESSING-开票中 SUCCESS-开票成功; FAIL-开票失败 ; WAIT_RED-待红冲 ; RED_PROGRESSING-红冲中 RED_SUCCESS-红冲成功 RED_FAIL-红冲失败")
  private InvoiceStatusEnum invoiceStatus;


  /**
   * 同步状态 状态: WAIT-待处理; DONE-已回传平台
   */
  @ApiModelProperty(value = "同步状态 状态: WAIT-待处理; DONE-已回传平台")
  private InvoiceStatusEnum syncStatus;


}
