package com.yxt.invoice.application.service;

import com.yxt.invoice.domain.command.ApplyInvoiceCommand;
import com.yxt.invoice.domain.command.ExistsThirdOrderInvoiceCommand;
import com.yxt.invoice.domain.command.OrderExistsInvoiceQueryCommand;
import com.yxt.invoice.domain.command.QueryInvoiceDetailCommand;
import com.yxt.invoice.domain.command.QueryInvoiceListCommand;
import com.yxt.invoice.domain.command.RedCreditInvoiceCommand;
import com.yxt.invoice.domain.command.user.UserQueryInvoiceListCommand;
import com.yxt.invoice.domain.model.InvoiceMain;
import com.yxt.invoice.domain.model.aggregate.InvoiceAggregate;
import com.yxt.invoice.domain.model.valueobject.ExistsOrderInvoice;
import com.yxt.lang.dto.api.PageDTO;

/**
 * 发票应用服务接口 Application层不依赖SDK，使用Domain对象
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
public interface InvoiceApplicationService {

  /**
   * 申请开票
   *
   * @param command 开票申请命令
   * @return 发票聚合根
   */
  String applyInvoice(ApplyInvoiceCommand command);

  /**
   * 发票红冲
   *
   * @param command 红冲命令
   * @return 发票聚合根
   */
  String applyRedInvoice(RedCreditInvoiceCommand command);

  /**
   * 查询发票列表
   *
   * @param query 列表查询
   * @return 发票列表
   */
  PageDTO<InvoiceMain> pageInvoiceList(QueryInvoiceListCommand query);

  /**
   * 查询发票详情
   *
   * @param query 详情查询
   * @return 发票聚合根
   */
  InvoiceAggregate queryInvoiceDetail(QueryInvoiceDetailCommand query);

  /**
   * 根据订单信息查询发票是否已经存在
   *
   * @param command
   * @return
   */
  ExistsOrderInvoice queryOrderExistsInvoice(OrderExistsInvoiceQueryCommand command);

  /**
   * 根据三方订单信息查询发票信息是否已经存在
   *
   * @param command
   * @return
   */
  ExistsOrderInvoice queryThirdOrderExistsInvoiceReqDto(ExistsThirdOrderInvoiceCommand command);

  /**
   * 用户发票记录查询
   * @param command
   * @return
   */
  PageDTO<InvoiceMain> userInvoiceList(UserQueryInvoiceListCommand command);
}
