package com.yxt.invoice.domain.external;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年04月03日 13:49
 * @email: <EMAIL>
 */
public interface IdService {

  String generateId(IdType idType);

  /**
   * ID类型
   */
  enum IdType {
    /**
     * 发票单号
     */
    INVOICE_MAIN_NO,
    /**
     * 发票明细单号
     */
    INVOICE_MAIN_DETAIL_NO,
    /**
     * 红冲对应的原单号
     */
    RED_INVOICE_MAIN_NO,

    /**
     * 红票发票明细
     */
    RED_INVOICE_MAIN_DETAIL_NO,

    /**
     * 发票抬头单号
     */
    INVOICE_TITLE_NO
  }
}
