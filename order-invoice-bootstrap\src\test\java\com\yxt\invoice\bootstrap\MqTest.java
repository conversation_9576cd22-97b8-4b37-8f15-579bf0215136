package com.yxt.invoice.bootstrap;

import com.fasterxml.jackson.databind.ser.Serializers.Base;
import com.yxt.invoice.interfaces.mq.rabbit.InvoiceRabbitMessageConsumer;
import javax.annotation.Resource;
import org.junit.Test;

public class MqTest extends BaseTest {

  @Resource
  private InvoiceRabbitMessageConsumer invoiceRabbitMessageConsumer;

  @Test
  public void test1(){
    invoiceRabbitMessageConsumer.callback("{\n"
        + "    \"outRequestCode\": \"1842303593478839814\",\n"
        + "    \"pId\": \"YXT00010\",\n"
        + "    \"channel\": \"压力测试\",\n"
        + "    \"responseId\": \"1000-5W2A-250904113235048\",\n"
        + "    \"invoiceTag\": \"0\",\n"
        + "    \"invoiceStatus\": \"06\",\n"
        + "    \"uploadStatus\": \"00\",\n"
        + "    \"statusMsg\": \"蓝票开具成功\",\n"
        + "    \"resetInvoice\": \"0\",\n"
        + "    \"invoicePdf\": \"http:\\/\\/10.0.38.115:8000\\/pdf\\/25997000000190778831\",\n"
        + "    \"downPdf\": \"http:\\/\\/10.0.38.115:8000\\/pdf\\/down\\/25997000000190778831.pdf\",\n"
        + "    \"issueDate\": \"2025-09-04 11:32:35\",\n"
        + "    \"invoiceCode\": \"25997000000190778831\",\n"
        + "    \"requestCode\": \"1000-5W2A-250904113235048\",\n"
        + "    \"sellerTin\": \"91530000725287862K\",\n"
        + "    \"taxAmount\": 1.02,\n"
        + "    \"amount\": 7.88,\n"
        + "    \"invoiceId\": 4924595\n"
        + "}");
  }

}
