package com.yxt.invoice.application.service.impl.builder;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Preconditions;
import com.yxt.domain.order.order_query.req.B2cOrderAllDetailReq;
import com.yxt.domain.order.order_query.res.B2cErpBillInfo;
import com.yxt.domain.order.order_query.res.B2cOmsOrderInfo;
import com.yxt.domain.order.order_query.res.B2cOrderAllDetailRes;
import com.yxt.domain.order.order_query.res.B2cOrderDetail;
import com.yxt.domain.order.order_query.res.B2cOrderPayInfo;
import com.yxt.domain.order.refund_query.req.B2cRefundAllDetailReq;
import com.yxt.domain.order.refund_query.req.B2cRefundPageSearchReq;
import com.yxt.domain.order.refund_query.res.B2cRefundAllDetailRes;
import com.yxt.domain.order.refund_query.res.B2cRefundDetail;
import com.yxt.domain.order.refund_query.res.B2cRefundOrderInfo;
import com.yxt.domain.order.refund_query.res.B2cRefundSimpleRes;
import com.yxt.invoice.application.third.baseinfo.feign.BaseInfoClient;
import com.yxt.invoice.application.third.baseinfo.feign.MemberClient;
import com.yxt.invoice.application.third.goods.dto.req.AveragePriceQuery;
import com.yxt.invoice.application.third.goods.dto.res.AveragePriceVO;
import com.yxt.invoice.application.third.goods.feign.MiddleMerchandiseClient;
import com.yxt.invoice.application.third.order.feign.B2COrderQueryDomainApiFeign;
import com.yxt.invoice.application.third.order.feign.B2CRefundQueryDomainApiFeign;
import com.yxt.invoice.domain.command.ApplyInvoiceCommand;
import com.yxt.invoice.domain.model.InvoiceDetail;
import com.yxt.invoice.domain.model.InvoiceMain;
import com.yxt.invoice.domain.model.valueobject.InvoiceAmount;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.middle.baseinfo.res.org.BizUnitOrgResDTO;
import com.yxt.middle.baseinfo.res.store.StoreInfoDataResDTO;
import com.yxt.middle.member.res.member.MemberInfoVo;
import com.yxt.order.common.base_order_dto.OrderPayInfo;
import com.yxt.order.common.utils.OrderDateUtil;
import com.yxt.order.types.DsConstants;
import com.yxt.order.types.invoice.enums.InvoiceAmountKeyEnum;
import com.yxt.order.types.invoice.enums.InvoiceBusinessTypeEnum;
import com.yxt.order.types.invoice.enums.InvoiceDeliveryTypeEnum;
import com.yxt.order.types.invoice.enums.InvoiceIsValidEnum;
import com.yxt.order.types.invoice.enums.InvoiceLineTypeEnum;
import com.yxt.order.types.invoice.enums.InvoiceStatusEnum;
import com.yxt.order.types.invoice.enums.InvoiceSyncStatusEnum;
import com.yxt.order.types.invoice.enums.InvoiceTransactionChannelEnum;
import com.yxt.order.types.offline.OfflineOrderNo;
import com.yxt.order.types.offline.OfflineThirdOrderNo;
import com.yxt.order.types.order.enums.PlatformCodeEnum;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
public class B2cCommandBuild extends AbstractApplyInvoiceCommandBuilder {

  @Resource
  private B2COrderQueryDomainApiFeign b2COrderQueryDomainApiFeign;

  @Resource
  private B2CRefundQueryDomainApiFeign b2CRefundQueryDomainApiFeign;

  @Resource
  private MiddleMerchandiseClient middleMerchandiseClient;

  @Resource
  private BaseInfoClient baseInfoClient;
  @Resource
  private MemberClient memberClient;

  @Override
  public Boolean route(ApplyInvoiceCommand command) {
    InvoiceMain invoiceMain = command.getInvoiceMain();
    return invoiceMain.getTransactionChannel().equals(InvoiceTransactionChannelEnum.ONLINE)
        && invoiceMain.getBusinessType().equals(InvoiceBusinessTypeEnum.B2C);
  }

  @Override
  public ApplyInvoiceCommand build(ApplyInvoiceCommand command) {
    InvoiceMain invoiceMain = command.getInvoiceMain();
    String orderNo = invoiceMain.getOrderNo().getOrderNo();
    String userId = invoiceMain.getUserId();
    String merCode = invoiceMain.getMerCode();

    B2cOrderAllDetailReq req = new B2cOrderAllDetailReq();
    req.setOmsOrderNo(orderNo);
    ResponseBase<B2cOrderAllDetailRes> omsOrderInfoResDtoResponseBase = b2COrderQueryDomainApiFeign.orderSearchByOrderNo(
        req);
    Preconditions.checkArgument(omsOrderInfoResDtoResponseBase.checkSuccess(),
        "查询线上单B2C数据异常,暂不支持开票");
    B2cOrderAllDetailRes allDetailRes = omsOrderInfoResDtoResponseBase.getData();
    B2cOmsOrderInfo omsOrderInfo = allDetailRes.getOmsOrderInfo();
    Preconditions.checkArgument(omsOrderInfo.getIsPostFeeOrder() == 0,
        "邮费单,暂不支持开票"); // 1-邮费单

    Preconditions.checkArgument(omsOrderInfo.getErpStatus() == 100, "订单下账状态,暂不支持开票");
    Preconditions.checkArgument(
        omsOrderInfo.getOrderStatus() != 101 && omsOrderInfo.getOrderStatus() != 102,
        "omsOrderNo"+omsOrderInfo.getOmsOrderNo()+"订单状态,暂不支持开票"+omsOrderInfo.getOrderStatus());
    B2cRefundPageSearchReq refundPageSearchReq = new B2cRefundPageSearchReq();
    refundPageSearchReq.setThirdOrderNos(Collections.singletonList(omsOrderInfo.getThirdOrderNo()));
    ResponseBase<PageDTO<B2cRefundSimpleRes>> pageDTOResponseBase = b2CRefundQueryDomainApiFeign.refundSearchPage(
        refundPageSearchReq);
    Preconditions.checkArgument(pageDTOResponseBase.checkSuccess(),
        "订单销售流水信息不全,暂不支持开票");

    if (StringUtils.isNotEmpty(omsOrderInfo.getMemberNo())) {
      ResponseBase<MemberInfoVo> memberByCardNo = memberClient.getMemberByCardNo(
          omsOrderInfo.getMemberNo());
      Preconditions.checkArgument(memberByCardNo.checkSuccess(), "查询会员信息失败,暂不支持开票");
      Preconditions.checkArgument(memberByCardNo.getData().getUserId().toString().equals(userId),
          "会员信息不一致,暂不支持开票");
    }
    List<B2cRefundAllDetailRes> refundDataList = new ArrayList<>();
    List<B2cRefundSimpleRes> tempRefundList = pageDTOResponseBase.getData().getData();
    for (B2cRefundSimpleRes dataRefund : tempRefundList) {
      B2cRefundAllDetailReq refundSearchByRefundNoReq = new B2cRefundAllDetailReq();
      refundSearchByRefundNoReq.setRefundNo(String.valueOf(dataRefund.getRefundNo()));
      ResponseBase<B2cRefundAllDetailRes> refundSearchByRefundNoResResponseBase = b2CRefundQueryDomainApiFeign.refundSearchByRefundNo(
          refundSearchByRefundNoReq);
      Preconditions.checkArgument(refundSearchByRefundNoResResponseBase.checkSuccess(),
          "查询退款单失败,暂不支持开票");
      B2cRefundAllDetailRes refundAllDetailRes = refundSearchByRefundNoResResponseBase.getData();
      B2cRefundOrderInfo dataRefundTemp = refundAllDetailRes.getRefundOrderInfo();
      if (Objects.equals(dataRefundTemp.getState(), "102") || Objects.equals(
          dataRefundTemp.getState(), "103")) {
        continue;
      }
      Preconditions.checkArgument(Objects.equals(dataRefundTemp.getState(), 100),
          "退款单状态,暂不支持开票"+dataRefundTemp.getState());
      Preconditions.checkArgument(dataRefundTemp.getErpState() == 100,
          "退款单下账状态,暂不支持开票"+dataRefundTemp.getErpState());
      refundDataList.add(refundAllDetailRes);
    }
    String storeCode = omsOrderInfo.getOrganizationCode();
    List<String> erpCodeList = allDetailRes.getOrderDetailList().stream()
        .map(B2cOrderDetail::getErpCode).distinct().collect(Collectors.toList());
    ResponseBase<List<AveragePriceVO>> detailListPriceResponse = middleMerchandiseClient.queryAveragePrice(
        DsConstants.SYSTEM, AveragePriceQuery.buildBean(merCode, storeCode, erpCodeList));
    Preconditions.checkArgument(detailListPriceResponse.checkSuccess(),
        "查询商品均价失败,暂不支持开票");
    ResponseBase<StoreInfoDataResDTO> storeInfo = baseInfoClient.getStoreInfo(merCode, null,
        storeCode);
    Preconditions.checkArgument(storeInfo.checkSuccess(), "查询门店信息失败,暂不支持开票");
    Optional<BizUnitOrgResDTO> first = storeInfo.getData().getBizUnitOrgList().stream()
        .filter(d -> "1".equals(d.getLayer())).findFirst();
    Preconditions.checkArgument(first.isPresent(), "查询门店所属公司信息失败,暂不支持开票");
    invoiceMain.setCompanyCode(first.get().getUnitCode());
    invoiceMain.setCompanyName(first.get().getUnitName());

    return applyInvoiceConvertByOnlineOrderB2C(allDetailRes, refundDataList, command,
        detailListPriceResponse.getData());
  }


  private ApplyInvoiceCommand applyInvoiceConvertByOnlineOrderB2C(B2cOrderAllDetailRes data,
      List<B2cRefundAllDetailRes> refundDataList, ApplyInvoiceCommand command,
      List<AveragePriceVO> detailPriceList) {
    B2cOmsOrderInfo baseOrderInfo = data.getOmsOrderInfo();
    InvoiceMain invoiceMain = command.getInvoiceMain();
    Date applyTime = invoiceMain.getApplyTime();
    String createdBy = invoiceMain.getCreatedBy();
    String deliveryType = InvoiceDeliveryTypeEnum.MERCHANT_FULFILLMENT.getCode();

    BigDecimal sumDetailInvoiceAmount = BigDecimal.ZERO;
    BigDecimal sumDetailTaxAmount = BigDecimal.ZERO;

    List<InvoiceAmount> invoiceAmounts = convertByOnlineOrderB2C(data, refundDataList);
    Optional<InvoiceAmount> first = invoiceAmounts.stream()
        .filter(invoiceAmount -> invoiceAmount.getKey().equals(command.getInvoiceAmount().getKey()))
        .findFirst();
    Preconditions.checkArgument(first.isPresent(), "请选择发票金额开具方式");
    BigDecimal invoiceAmount = first.get().getAmount();

    Map<String, AveragePriceVO> priceVOMap = detailPriceList.stream()
        .collect(Collectors.toMap(AveragePriceVO::getErpCode, d -> d));
    Map<String, List<B2cRefundDetail>> mapByRefundDetailList = refundDataList.stream()
        .flatMap(model -> model.getRefundDetailList().stream())
        .collect(Collectors.groupingBy(B2cRefundDetail::getThirdDetailId));

    List<InvoiceDetail> invoiceDetails = new ArrayList<>();
    Integer line = 1;
    for (B2cOrderDetail orderDetail : data.getOrderDetailList()) {
      if (orderDetail.getStatus() != 0) {
        continue;
      }
      Integer commodityCount = orderDetail.getCommodityCount();
      String rowNo = orderDetail.getThirdDetailId();
      List<B2cRefundDetail> refundDetails = mapByRefundDetailList.get(rowNo);
      if (!CollectionUtils.isEmpty(refundDetails)) {
        for (B2cRefundDetail refundDetail : refundDetails) {
          commodityCount = commodityCount - (refundDetail.getRefundCount());
        }
      }
      if (commodityCount.intValue() <= 0) {
        continue;
      }
      AveragePriceVO averagePriceVO = priceVOMap.get(orderDetail.getErpCode());
      BigDecimal taxRate = new BigDecimal(averagePriceVO.getTaxRate());
      BigDecimal price = orderDetail.getPrice();
      BigDecimal totalAmount = price.multiply(new BigDecimal(commodityCount));  //todo 改新字段 俊峰加
      BigDecimal taxAmount = totalAmount.multiply(taxRate).setScale(2, RoundingMode.HALF_UP);
      BigDecimal PriceTaxAmount = totalAmount.add(taxAmount);

      sumDetailInvoiceAmount = sumDetailInvoiceAmount.add(totalAmount);
      sumDetailTaxAmount = sumDetailTaxAmount.add(taxAmount);

      InvoiceDetail invoiceDetail = new InvoiceDetail();
      invoiceDetail.setRowNo(rowNo);
      invoiceDetail.setLine(String.valueOf(line));
      invoiceDetail.setErpCode(orderDetail.getErpCode());
      invoiceDetail.setErpName(orderDetail.getCommodityName());
      invoiceDetail.setCommodityCount(new BigDecimal(commodityCount));
      invoiceDetail.setPrice(price);
      invoiceDetail.setTotalAmount(totalAmount);
      invoiceDetail.setTaxAmount(taxAmount);
      invoiceDetail.setTaxRate(taxRate);
      invoiceDetail.setPriceTaxAmount(PriceTaxAmount);
      invoiceDetail.setInvoiceLineType(InvoiceLineTypeEnum.REGULAR_LINE.getCode());
      invoiceDetail.setDiscountAmount(BigDecimal.ZERO);
      invoiceDetail.setPolicyStatus("NO");
      invoiceDetail.setPolicyTaxRate(null);
      invoiceDetail.setIsValid(InvoiceIsValidEnum.VALID.getCode());
      invoiceDetail.setCreated(applyTime);
      invoiceDetail.setUpdated(applyTime);
      invoiceDetail.setCreatedBy(createdBy);
      invoiceDetail.setUpdatedBy(createdBy);
      invoiceDetail.setVersion(1L);
      invoiceDetails.add(invoiceDetail);

      line = line + 1;
    }

    Preconditions.checkArgument(invoiceAmount.compareTo(sumDetailInvoiceAmount) != 0,
        "发票头与商品行合计金额不一致");
    BigDecimal priceTaxAmount = sumDetailInvoiceAmount.add(sumDetailTaxAmount);

    invoiceMain.setCompanyCode(command.getInvoiceMain().getCompanyCode());
    invoiceMain.setCompanyName(command.getInvoiceMain().getCompanyName());
    invoiceMain.setOrganizationCode(baseOrderInfo.getOrganizationCode());
    invoiceMain.setOrganizationName(baseOrderInfo.getOrganizationName());
    invoiceMain.setThirdPlatformCode(baseOrderInfo.getThirdPlatformCode());
    invoiceMain.setThirdOrderNo(OfflineThirdOrderNo.thirdOrderNo(baseOrderInfo.getThirdOrderNo()));
    invoiceMain.setOrderNo(OfflineOrderNo.orderNo(String.valueOf(baseOrderInfo.getOrderNo())));
    invoiceMain.setPosNo(baseOrderInfo.getErpSaleNo());
    invoiceMain.setInvoiceStatus(InvoiceStatusEnum.WAIT);
    invoiceMain.setSyncStatus(InvoiceSyncStatusEnum.WAIT);
    invoiceMain.setActualPayAmount(invoiceAmount);
    invoiceMain.setDeliveryAmount(BigDecimal.ZERO);
    invoiceMain.setDeliveryType(deliveryType);
    invoiceMain.setInvoiceAmount(invoiceAmount);
    invoiceMain.setTaxAmount(sumDetailTaxAmount);
    invoiceMain.setPriceTaxAmount(priceTaxAmount);
    invoiceMain.setSplitBill("NOT");
    invoiceMain.setOrderCreated(OrderDateUtil.LocalDateTimeToDate(baseOrderInfo.getCreated()));
    invoiceMain.setSellerNumber(baseOrderInfo.getOrganizationCode());
    invoiceMain.setSellerName(baseOrderInfo.getOrganizationName());
    invoiceMain.setIsValid(InvoiceIsValidEnum.VALID.getCode());
    invoiceMain.setCreated(new Date());
    invoiceMain.setUpdated(new Date());
    invoiceMain.setVersion(1L);

    command.setInvoiceMain(invoiceMain);
    command.setDetails(invoiceDetails);
    command.setThirdOrderNo(baseOrderInfo.getThirdOrderNo());

    return command;


  }


  public List<InvoiceAmount> convertByOnlineOrderB2C(B2cOrderAllDetailRes data,
      List<B2cRefundAllDetailRes> refundDataList) {
    B2cOrderPayInfo orderPayInfo = data.getPayInfo();
    B2cErpBillInfo erpBillInfo = data.getErpBillInfo();
    BigDecimal buyerActualAmount = orderPayInfo.getBuyerActualAmount();//todo 改新字段 俊峰加

    BigDecimal xy_cash = BigDecimal.ZERO;
    // 新增现金券金额
    List<OrderPayInfo.PaySaleInfo> paySaleInfos = JSON.parseArray(orderPayInfo.getPaySaleInfo(),
        OrderPayInfo.PaySaleInfo.class);
    if (CollUtil.isNotEmpty(paySaleInfos)) {
      xy_cash = paySaleInfos.stream().map(OrderPayInfo.PaySaleInfo::getSaleAmount)
          .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add)
          .setScale(2, RoundingMode.HALF_UP);
    }
    BigDecimal subtract = buyerActualAmount.subtract(xy_cash);
    for (B2cRefundAllDetailRes refundDetail : refundDataList) {
      subtract = subtract.subtract(refundDetail.getRefundOrderInfo().getConsumerRefund());
    }

    List<InvoiceAmount> invoiceAmounts = new ArrayList<>();

    invoiceAmounts.add(new InvoiceAmount(InvoiceAmountKeyEnum.INVOICE_AMOUNT, subtract));
    BigDecimal invoiceAmountWithPostFee = subtract.add(
        orderPayInfo.getDeliveryFee().subtract(orderPayInfo.getPostFeeDiscount()));
    BigDecimal invoiceAmountWithPostFeeWithSubsidy = invoiceAmountWithPostFee.add(
        orderPayInfo.getPlatformDiscount());
    if (data.getOmsOrderInfo().getThirdPlatformCode()
        .equals(PlatformCodeEnum.JD_DAOJIA.getCode())) {
      invoiceAmountWithPostFeeWithSubsidy = erpBillInfo.getBillTotalAmount();
    }
    invoiceAmounts.add(new InvoiceAmount(InvoiceAmountKeyEnum.INVOICE_AMOUNT_WITH_POST_FEE,
        invoiceAmountWithPostFee));
    invoiceAmounts.add(
        new InvoiceAmount(InvoiceAmountKeyEnum.INVOICE_AMOUNT_WITH_POST_FEE_WITH_SUBSIDY,
            invoiceAmountWithPostFeeWithSubsidy));

    return invoiceAmounts;
  }

}
