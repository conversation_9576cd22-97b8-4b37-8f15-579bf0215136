package com.yxt.invoice.domain.command.title;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 发票抬头详情查询命令
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-06
 */
@Data
public class InvoiceTitleDetailCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 抬头内部单号
     */
    @NotBlank(message = "抬头内部单号不能为空")
    @Size(max = 50, message = "抬头内部单号长度不能超过50个字符")
    private String invoiceTitleNo;
}
