package com.yxt.invoice.application.service.impl.exists_invoice_query;

import com.google.common.base.Preconditions;
import com.yxt.domain.order.order_query.req.OrderSearchByOrderNoReqInvoice;
import com.yxt.domain.order.order_query.res.OrderDomainRelatedResInvoice;
import com.yxt.domain.order.refund_query.req.RefundPageSearchReq;
import com.yxt.domain.order.refund_query.req.RefundSearchByRefundNoReq;
import com.yxt.domain.order.refund_query.res.RefundDomainRelatedRes;
import com.yxt.domain.order.refund_query.res.RefundSimpleRes;
import com.yxt.invoice.application.service.impl.builder.O2oCommandBuild;
import com.yxt.invoice.application.third.order.feign.OrderQueryDomainApiFeign;
import com.yxt.invoice.application.third.order.feign.RefundQueryDomainApiFeign;
import com.yxt.invoice.domain.command.OrderExistsInvoiceQueryCommand;
import com.yxt.invoice.domain.model.valueobject.ExistsOrderInvoice;
import com.yxt.invoice.domain.model.valueobject.InvoiceAmount;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.common.base_order_dto.OrderInfo;
import com.yxt.order.types.invoice.enums.InvoiceBusinessTypeEnum;
import com.yxt.order.types.invoice.enums.InvoiceTransactionChannelEnum;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import javax.annotation.Resource;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

@Component
public class ExistInvoiceO2O extends AbstractExistsInvoiceQuery {


  @Resource
  private O2oCommandBuild o2oCommandBuild;

  @Resource
  private OrderQueryDomainApiFeign orderQueryDomainApiFeign;

  @Resource
  private RefundQueryDomainApiFeign refundQueryDomainApiFeign;

  @Override
  public Boolean route(InvoiceBusinessTypeEnum businessTypeEnum) {
    return InvoiceBusinessTypeEnum.O2O.equals(businessTypeEnum);
  }


  @Override
  public ExistsOrderInvoice build(OrderExistsInvoiceQueryCommand command) {
    String orderNo = command.getOrderNo();

    ExistsOrderInvoice existsOrderInvoice = new ExistsOrderInvoice();

    OrderSearchByOrderNoReqInvoice req = new OrderSearchByOrderNoReqInvoice();
    req.setOrderNo(orderNo);
    ResponseBase<OrderDomainRelatedResInvoice> orderDomainRelatedResResponseBase = orderQueryDomainApiFeign.orderSearchByOrderNoInvoice(
        req);
    Preconditions.checkArgument(orderDomainRelatedResResponseBase.checkSuccess(),
        "查询线上单O2O数据异常,暂不支持开票");
    OrderDomainRelatedResInvoice data = orderDomainRelatedResResponseBase.getData();
    OrderInfo orderInfo = data.getOrderInfo();
    Preconditions.checkArgument(orderInfo.getErpState() == 100, "订单下账状态,暂不支持开票");
    Preconditions.checkArgument(
        orderInfo.getOrderState() != 101 && orderInfo.getOrderState() != 102,
        orderInfo.getOrderNo()+"订单状态,暂不支持开票"+orderInfo.getOrderState());
    Preconditions.checkArgument(StringUtils.isNotEmpty(orderInfo.getErpSaleNo()),
        "订单销售流水信息不全,暂不支持开票");
    RefundPageSearchReq refundPageSearchReq = new RefundPageSearchReq();
    refundPageSearchReq.setOrderNoList(Collections.singletonList(orderNo));
    ResponseBase<PageDTO<RefundSimpleRes>> pageDTOResponseBase = refundQueryDomainApiFeign.refundSearchPage(
        refundPageSearchReq);
    Preconditions.checkArgument(pageDTOResponseBase.checkSuccess(),
        "订单销售流水信息不全,暂不支持开票");

    List<RefundDomainRelatedRes> refundDataList = new ArrayList<>();
    List<RefundSimpleRes> tempRefundList = pageDTOResponseBase.getData().getData();
    for (RefundSimpleRes dataRefund : tempRefundList) {
      RefundSearchByRefundNoReq refundSearchByRefundNoReq = new RefundSearchByRefundNoReq();
      refundSearchByRefundNoReq.setRefundNo(dataRefund.getRefundNo());
      ResponseBase<RefundDomainRelatedRes> refundDomainRelatedResResponseBase = refundQueryDomainApiFeign.refundSearchByRefundNo(
          refundSearchByRefundNoReq);
      Preconditions.checkArgument(refundDomainRelatedResResponseBase.checkSuccess(),
          "查询退款单失败,暂不支持开票");
      RefundDomainRelatedRes dataRefundTemp = refundDomainRelatedResResponseBase.getData();
      if (dataRefundTemp.getRefundOrder().getState() == 102
          || dataRefundTemp.getRefundOrder().getState() == 103) {
        continue;
      }
      Preconditions.checkArgument(dataRefundTemp.getRefundOrder().getState() == 100,
          "退款单状态,暂不支持开票" + dataRefundTemp.getRefundOrder().getState());
      Preconditions.checkArgument(dataRefundTemp.getRefundOrder().getErpState() == 100,
          "退款单下账状态,暂不支持开票" + dataRefundTemp.getRefundOrder().getErpState());
      refundDataList.add(dataRefundTemp);
    }
    List<InvoiceAmount> invoiceAmountList = o2oCommandBuild.convertByOnlineOrderO2O(data,
        refundDataList);

    existsOrderInvoice.setInvoiceAmounts(invoiceAmountList);
    existsOrderInvoice.setThirdPlatformCode(orderInfo.getThirdPlatformCode());
    existsOrderInvoice.setThirdOrderNo(orderInfo.getThirdOrderNo());
    existsOrderInvoice.setOrderNo(String.valueOf(orderInfo.getOrderNo()));
    existsOrderInvoice.setPosNo(orderInfo.getErpSaleNo());
    existsOrderInvoice.setTransactionChannel(InvoiceTransactionChannelEnum.ONLINE);
    existsOrderInvoice.setBusinessType(InvoiceBusinessTypeEnum.O2O);
    return existsOrderInvoice;
  }

}
