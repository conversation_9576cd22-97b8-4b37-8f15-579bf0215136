package com.yxt.invoice.interfaces.controller;

import com.yxt.invoice.domain.model.InvoiceMain;
import com.yxt.invoice.domain.model.aggregate.InvoiceAggregate;
import com.yxt.invoice.domain.repository.InvoiceRepository;
import com.yxt.invoice.interfaces.service.InvoiceService;
import com.yxt.invoice.sdk.api.InvoiceOpsApi;
import com.yxt.invoice.sdk.dto.req.SyncInvoiceFromRemoteReq;
import com.yxt.invoice.sdk.dto.req.UpdateInvoiceFromRemoteReq;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.types.invoice.enums.InvoiceRedBlueTypeEnum;
import com.yxt.order.types.invoice.remote.RemoteInvoiceTag;
import io.swagger.annotations.ApiOperation;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 发票控制器 实现SDK接口，提供RESTful API
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@ApiOperation(value = "运维接口")
@RestController
@RequestMapping("/api/1.0/invoice/ops")
@Slf4j
public class InvoiceOpsController implements InvoiceOpsApi {

  @Resource
  private InvoiceRepository invoiceRepository;

  @Resource
  private InvoiceService invoiceService;

  @Override
  public ResponseBase<String> updateInvoiceFromRemote(UpdateInvoiceFromRemoteReq req) {
    InvoiceAggregate aggregate = invoiceRepository.findByInvoiceMainNo(req.getInvoiceMainNo());

    InvoiceMain invoiceMain = aggregate.getInvoiceMain();
    String remoteInvoiceResponseId = invoiceMain.getRemoteInvoiceResponseId();
    String invoiceMainNo = invoiceMain.getInvoiceMainNo();
    InvoiceRedBlueTypeEnum invoiceRedBlueType = invoiceMain.getInvoiceRedBlueType();

    String invoiceTag = RemoteInvoiceTag.mapping(invoiceRedBlueType).getCode();

    invoiceService.updateInvoiceFromOrderDetail(remoteInvoiceResponseId, invoiceMainNo, invoiceTag);

    return ResponseBase.success(invoiceMainNo);
  }

  @Override
  public ResponseBase<String> syncInvoiceFromRemote(SyncInvoiceFromRemoteReq req) {
    req.valid();


    return null;
  }
}
