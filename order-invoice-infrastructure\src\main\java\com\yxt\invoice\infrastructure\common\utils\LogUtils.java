package com.yxt.invoice.infrastructure.common.utils;

import cn.hutool.extra.spring.SpringUtil;
import com.yxt.invoice.infrastructure.common.UserContext;
import com.yxt.invoice.infrastructure.db.mysql.entity.InvoiceLogApiDO;
import com.yxt.invoice.infrastructure.db.mysql.entity.InvoiceLogDO;
import com.yxt.invoice.infrastructure.db.mysql.mapper.InvoiceLogApiMapper;
import com.yxt.invoice.infrastructure.db.mysql.mapper.InvoiceLogMapper;
import java.util.Date;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class LogUtils {


  public static void log(String invoiceMainNo, String content, String logType) {
    log.info("log {} - {} - {}", invoiceMainNo, content, logType);
    InvoiceLogMapper invoiceLogMapper = SpringUtil.getBean(InvoiceLogMapper.class);
    if (Objects.isNull(invoiceLogMapper)) {
      log.warn("日志存储未初始化");
      return;
    }
    String userId = UserContext.getCurrentUserId();
    String userName = UserContext.getCurrentUserName();

    InvoiceLogDO invoiceLogDO = new InvoiceLogDO();
    invoiceLogDO.setInvoiceMainNo(invoiceMainNo);
    invoiceLogDO.setOperatorId(StringUtils.isNotEmpty(userId) ? userId : "system");
    invoiceLogDO.setOperatorName(StringUtils.isNotEmpty(userName) ? userName : "system");
    invoiceLogDO.setContent(content);
    invoiceLogDO.setLogType(logType);
    invoiceLogDO.setCreateTime(new Date());
    invoiceLogMapper.insert(invoiceLogDO);
  }


  public static void logApi(String businessNo, String content, String logType, String position,String desc) {
    log.info("logApi {} - {} - {}", businessNo, content, logType);
    InvoiceLogApiMapper invoiceLogApiMapper = SpringUtil.getBean(InvoiceLogApiMapper.class);
    if (Objects.isNull(invoiceLogApiMapper)) {
      log.warn("Api 日志存储未初始化");
      return;
    }
    String userId = UserContext.getCurrentUserId();
    String userName = UserContext.getCurrentUserName();

    InvoiceLogApiDO api = new InvoiceLogApiDO();
    api.setBusinessNo(businessNo);
    api.setDescription(desc);
    api.setOperatorId(StringUtils.isNotEmpty(userId) ? userId : "system");
    api.setOperatorName(StringUtils.isNotEmpty(userName) ? userName : "system");
    api.setContent(content);
    api.setLogType(logType);
    api.setPosition(position);
    api.setCreateTime(new Date());
    invoiceLogApiMapper.insert(api);
  }

}
