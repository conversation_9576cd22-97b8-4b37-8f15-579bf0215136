package com.yxt.invoice.interfaces.mq.rabbit;

import com.fasterxml.jackson.core.type.TypeReference;
import com.rabbitmq.client.Channel;
import com.yxt.invoice.domain.event.callback.InvoiceBaseMsgModel;
import com.yxt.invoice.infrastructure.common.utils.LogUtils;
import com.yxt.invoice.interfaces.service.InvoiceService;
import com.yxt.lang.util.JsonUtils;
import com.yxt.order.types.invoice.enums.InvoiceLogTypeApiEnum;
import java.io.IOException;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

/**
 * 发票相关RabbitMQ消息监听器
 *
 * <AUTHOR>
 * @since 2025-08-21
 */
@Component
@Slf4j
public class InvoiceRabbitMessageConsumer {


  @Resource
  private InvoiceService invoiceService;


  /**
   * 处理流程: MQ通知 --> 详情 --> 基于详情处理结果
   */
  @RabbitListener(queues = "${rabbit.consumer.tax-cloud-topic}", containerFactory = "multiRabbitListenerContainerFactory")
  @RabbitHandler
  public void receiveBroadcastB2C(Message message, Channel channel) throws IOException {

    try {
      String msg = new String(message.getBody());
      log.info("税务云发票 回调通知:{}", msg);

      callback(msg);

      //ACK,确认一条消息已经被消费
      channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
    } catch (Exception e) {
      //NACK basicNack(deliveryTag, multiple, requeue)
      channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
      throw new RuntimeException(e);
    }
  }

  public void callback(String msg) {
    // 记录到日志表
    InvoiceBaseMsgModel invoiceBaseMsgModel = fetchBaseAndLog(msg);

    String outRequestCode = invoiceBaseMsgModel.getOutRequestCode();
    String invoiceTag = invoiceBaseMsgModel.getInvoiceTag();
    String responseId = invoiceBaseMsgModel.getResponseId();
    try {
      // 调用详情接口,并根据详情来更新订单数据
      invoiceService.updateInvoiceFromOrderDetail(responseId, outRequestCode, invoiceTag);
    } catch (Exception e) {
      log.error("暂不支持该业务场景:{}", msg);
      throw new RuntimeException(e);
    }
  }


  private InvoiceBaseMsgModel fetchBaseAndLog(String msg) {
    try {
      InvoiceBaseMsgModel baseMsgModel = JsonUtils.toObject(msg,
          new TypeReference<InvoiceBaseMsgModel>() {
          });
      String outRequestCode = baseMsgModel.getOutRequestCode(); //我们系统内部订单号
      LogUtils.logApi(outRequestCode, msg, InvoiceLogTypeApiEnum.MQ.name(), getPosition(),
          "税务云MQ返回");
      return baseMsgModel;
    } catch (Exception e) {
      LogUtils.logApi("InvoiceBaseMsgModel parse error", msg, InvoiceLogTypeApiEnum.MQ.name(),
          getPosition(), "税务云MQ返回");
      throw new RuntimeException("转换异常记录原始消息");
    }

  }

  @NotNull
  private String getPosition() {
    return this.getClass().getName() + ":tid:" + Thread.currentThread().getId();
  }
}
