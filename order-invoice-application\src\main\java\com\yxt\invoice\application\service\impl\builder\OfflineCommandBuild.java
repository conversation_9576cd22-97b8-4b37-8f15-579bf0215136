package com.yxt.invoice.application.service.impl.builder;

import com.google.common.base.Preconditions;
import com.yxt.invoice.application.third.goods.dto.req.AveragePriceQuery;
import com.yxt.invoice.application.third.goods.dto.res.AveragePriceVO;
import com.yxt.invoice.application.third.goods.feign.MiddleMerchandiseClient;
import com.yxt.invoice.application.third.order.feign.OfflineOrderQueryApiFeign;
import com.yxt.invoice.domain.command.ApplyInvoiceCommand;
import com.yxt.invoice.domain.model.InvoiceDetail;
import com.yxt.invoice.domain.model.InvoiceMain;
import com.yxt.invoice.domain.model.valueobject.InvoiceAmount;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.open.message.offline_order.model.CommonOfflineRefundOrderModel;
import com.yxt.order.open.message.offline_order.model.OfflineOrderModel;
import com.yxt.order.open.message.offline_order.model.OfflineRefundOrderModel;
import com.yxt.order.open.sdk.offline_order.req.CommonOfflineRefundOrderQueryReqDto;
import com.yxt.order.open.sdk.offline_order.req.OfflineOrderDetailQueryReqDto;
import com.yxt.order.open.sdk.offline_order.req.OfflineRefundOrderDetailQueryReqDto;
import com.yxt.order.types.DsConstants;
import com.yxt.order.types.invoice.enums.InvoiceAmountKeyEnum;
import com.yxt.order.types.invoice.enums.InvoiceDeliveryTypeEnum;
import com.yxt.order.types.invoice.enums.InvoiceIsValidEnum;
import com.yxt.order.types.invoice.enums.InvoiceLineTypeEnum;
import com.yxt.order.types.invoice.enums.InvoiceStatusEnum;
import com.yxt.order.types.invoice.enums.InvoiceSyncStatusEnum;
import com.yxt.order.types.invoice.enums.InvoiceTransactionChannelEnum;
import com.yxt.order.types.offline.enums.CouponTypeEnum;
import com.yxt.order.types.offline.enums.DataDimensionTypeEnum;
import com.yxt.order.types.offline.enums.RefundTypeEnum;
import com.yxt.order.types.offline.refund.OfflineRefundNo;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
public class OfflineCommandBuild extends AbstractApplyInvoiceCommandBuilder {


  @Resource
  private OfflineOrderQueryApiFeign offlineOrderQueryApiFeign;

  @Resource
  private MiddleMerchandiseClient middleMerchandiseClient;


  @Override
  public Boolean route(ApplyInvoiceCommand command) {
    return command.getInvoiceMain().getTransactionChannel()
        .equals(InvoiceTransactionChannelEnum.OFFLINE);
  }

  @Override
  public ApplyInvoiceCommand build(ApplyInvoiceCommand command) {

    InvoiceMain invoiceMain = command.getInvoiceMain();
    String orderNo = invoiceMain.getOrderNo().getOrderNo();
    String userId = invoiceMain.getUserId();
    String merCode = invoiceMain.getMerCode();

    OfflineOrderDetailQueryReqDto queryReqDto = new OfflineOrderDetailQueryReqDto();
    queryReqDto.setOrderNo(orderNo);
    //查询线下单补充
    ResponseBase<OfflineOrderModel> offlineOrderModelResponseBase = offlineOrderQueryApiFeign.detail(
        queryReqDto);
    Preconditions.checkArgument(offlineOrderModelResponseBase.checkSuccess(),
        "查询线下单数据异常,暂不支持开票");

    OfflineOrderModel data = offlineOrderModelResponseBase.getData();
    if (null != data.getBaseUserInfo().getUserId() && StringUtils.isNotEmpty(userId)) {
      Preconditions.checkArgument(data.getBaseUserInfo().getUserId().getUserId().equals(userId),
          "此订单为其他会员所有,请核对信息申请");
    }

    CommonOfflineRefundOrderQueryReqDto reqDto = new CommonOfflineRefundOrderQueryReqDto();
    reqDto.setOrderNo(orderNo);

    ResponseBase<CommonOfflineRefundOrderModel> refundRes = offlineOrderQueryApiFeign.commonRefundInfo(
        reqDto);
    if(!refundRes.getCode().equals("60162")){
      Preconditions.checkArgument(refundRes.checkSuccess(), "查询线下单退单数据异常,暂不支持开票");
    }

    List<OfflineRefundOrderModel> refundDataList = new ArrayList<>();

    if (Objects.nonNull(refundRes.getData()) && !CollectionUtils.isEmpty(
        refundRes.getData().getRefundOrderModelList())) {
      for (OfflineRefundOrderModel offlineRefundOrderModel : refundRes.getData()
          .getRefundOrderModelList()) {

        OfflineRefundNo refundNo = offlineRefundOrderModel.getBaseRefundInfo().getRefundNo();
        OfflineRefundOrderDetailQueryReqDto refundDetailQueryReqDto = new OfflineRefundOrderDetailQueryReqDto();
        refundDetailQueryReqDto.setRefundNo(refundNo.getRefundNo());
        ResponseBase<OfflineRefundOrderModel> offlineRefundOrderModelResponseBase = offlineOrderQueryApiFeign.refundDetail(
            refundDetailQueryReqDto);
        Preconditions.checkArgument(offlineRefundOrderModelResponseBase.checkSuccess(),
            "查询线下单退单数据异常,暂不支持开票");
        Preconditions.checkArgument(
            !offlineRefundOrderModelResponseBase.getData().getBaseRefundInfo().getRefundTypeValue()
                .equals(RefundTypeEnum.ALL.name()), "暂不支持整单退开票");
        refundDataList.add(offlineRefundOrderModelResponseBase.getData());
      }
    }

    String storeCode = data.getBaseOrganizationInfo().getStoreCode();
    List<String> erpCodeList = data.getOrderDetailList().stream()
        .map(d -> d.getBaseOrderDetailInfo().getErpCode().getErpCode()).distinct()
        .collect(Collectors.toList());
    ResponseBase<List<AveragePriceVO>> detailListPriceResponse = middleMerchandiseClient.queryAveragePrice(
        DsConstants.SYSTEM, AveragePriceQuery.buildBean(merCode, storeCode, erpCodeList));
    Preconditions.checkArgument(detailListPriceResponse.checkSuccess(),
        "查询商品平均价失败,暂不支持开票");

    return applyInvoiceConvertByOfflineOrder(data, refundDataList, command,
        detailListPriceResponse.getData());
  }


  ApplyInvoiceCommand applyInvoiceConvertByOfflineOrder(OfflineOrderModel data,
      List<OfflineRefundOrderModel> refundDataList, ApplyInvoiceCommand command,
      List<AveragePriceVO> detailPriceList) {
    OfflineOrderModel.OfflineOrderInfo baseOrderInfo = data.getBaseOrderInfo();
    OfflineOrderModel.Organization baseOrganizationInfo = data.getBaseOrganizationInfo();
    InvoiceMain invoiceMain = command.getInvoiceMain();
    Date applyTime = invoiceMain.getApplyTime();
    String createdBy = invoiceMain.getCreatedBy();

    BigDecimal sumDetailInvoiceAmount = BigDecimal.ZERO;
    BigDecimal sumDetailTaxAmount = BigDecimal.ZERO;

    List<InvoiceAmount> invoiceAmounts = convertByOfflineOrder(data, refundDataList);
    Optional<InvoiceAmount> first = invoiceAmounts.stream()
        .filter(invoiceAmount -> invoiceAmount.getKey().equals(command.getInvoiceAmount().getKey()))
        .findFirst();
    Preconditions.checkArgument(first.isPresent(), "请选择发票金额开具方式");
    BigDecimal invoiceAmount = first.get().getAmount();
    BigDecimal actualPayAmount = invoiceAmount;

    Map<String, AveragePriceVO> priceVOMap = detailPriceList.stream()
        .collect(Collectors.toMap(AveragePriceVO::getErpCode, d -> d));
    Map<String, List<OfflineRefundOrderModel.RefundDetail>> mapByRefundDetailList = refundDataList.stream()
        .flatMap(model -> model.getRefundDetailList().stream())
        .collect(Collectors.groupingBy(OfflineRefundOrderModel.RefundDetail::getRowNo));

    Integer line = 1;

    List<InvoiceDetail> invoiceDetails = new ArrayList<>();
    for (OfflineOrderModel.OrderDetail orderDetail : data.getOrderDetailList()) {
      BigDecimal commodityCount = orderDetail.getBaseOrderDetailInfo().getCommodityCount();
      String rowNo = orderDetail.getBaseOrderDetailInfo().getRowNo();
      List<OfflineRefundOrderModel.RefundDetail> refundDetails = mapByRefundDetailList.get(rowNo);
      if (!CollectionUtils.isEmpty(refundDetails)) {
        for (OfflineRefundOrderModel.RefundDetail refundDetail : refundDetails) {
          commodityCount = commodityCount.subtract(refundDetail.getRefundCount());
        }
      }
      if (commodityCount.intValue() <= 0) {
        continue;
      }
      AveragePriceVO averagePriceVO = priceVOMap.get(
          orderDetail.getBaseOrderDetailInfo().getErpCode().getErpCode());
      BigDecimal taxRate = new BigDecimal(averagePriceVO.getTaxRate());
      BigDecimal price = orderDetail.getBaseOrderDetailInfo().getPrice();
      BigDecimal totalAmount = price.multiply(commodityCount);
      BigDecimal taxAmount = totalAmount.multiply(taxRate).setScale(2, RoundingMode.HALF_UP);
      BigDecimal PriceTaxAmount = totalAmount.add(taxAmount);

      sumDetailInvoiceAmount = sumDetailInvoiceAmount.add(totalAmount);
      sumDetailTaxAmount = sumDetailTaxAmount.add(taxAmount);

      InvoiceDetail invoiceDetail = new InvoiceDetail();
      invoiceDetail.setRowNo(rowNo);
      invoiceDetail.setLine(String.valueOf(line));
      invoiceDetail.setErpCode(orderDetail.getBaseOrderDetailInfo().getErpCode().getErpCode());
      invoiceDetail.setErpName(orderDetail.getBaseOrderDetailInfo().getErpName());
      invoiceDetail.setCommodityCount(commodityCount);
      invoiceDetail.setPrice(price);
      invoiceDetail.setTotalAmount(totalAmount);
      invoiceDetail.setTaxAmount(taxAmount);
      invoiceDetail.setTaxRate(taxRate);
      invoiceDetail.setPriceTaxAmount(PriceTaxAmount);
      invoiceDetail.setInvoiceLineType(InvoiceLineTypeEnum.REGULAR_LINE.getCode());
      invoiceDetail.setDiscountAmount(BigDecimal.ZERO);
      invoiceDetail.setPolicyStatus("NO");
      invoiceDetail.setPolicyTaxRate(null);
      invoiceDetail.setIsValid(InvoiceIsValidEnum.VALID.getCode());
      invoiceDetail.setCreated(applyTime);
      invoiceDetail.setUpdated(applyTime);
      invoiceDetail.setCreatedBy(createdBy);
      invoiceDetail.setUpdatedBy(createdBy);
      invoiceDetail.setVersion(1L);
      invoiceDetails.add(invoiceDetail);

      line = line + 1;
    }

    Preconditions.checkArgument(!CollectionUtils.isEmpty(invoiceDetails),
        "该订单无商品明细。可能原因: 正单退,无有效明细");

    Preconditions.checkArgument(invoiceAmount.compareTo(sumDetailInvoiceAmount) == 0,
        "发票头与商品行合计金额不一致");
    BigDecimal priceTaxAmount = sumDetailInvoiceAmount.add(sumDetailTaxAmount);

    invoiceMain.setCompanyCode(baseOrganizationInfo.getCompanyCode());
    invoiceMain.setCompanyName(baseOrganizationInfo.getCompanyName());
    invoiceMain.setOrganizationCode(baseOrganizationInfo.getStoreCode());
    invoiceMain.setOrganizationName(baseOrganizationInfo.getStoreName());
    invoiceMain.setThirdPlatformCode(baseOrderInfo.getThirdPlatformCodeValue());
    invoiceMain.setThirdOrderNo(baseOrderInfo.getThirdOrderNo());
    invoiceMain.setOrderNo(baseOrderInfo.getOrderNo());
    invoiceMain.setPosNo(baseOrderInfo.getThirdOrderNo().getThirdOrderNo());
    invoiceMain.setInvoiceStatus(InvoiceStatusEnum.WAIT);
    invoiceMain.setSyncStatus(InvoiceSyncStatusEnum.WAIT);
    invoiceMain.setActualPayAmount(actualPayAmount);
    invoiceMain.setDeliveryAmount(BigDecimal.ZERO);
    invoiceMain.setDeliveryType(InvoiceDeliveryTypeEnum.SELF_PICK_UP.getCode());
    invoiceMain.setInvoiceAmount(invoiceAmount);
    invoiceMain.setTaxAmount(sumDetailTaxAmount);
    invoiceMain.setPriceTaxAmount(priceTaxAmount);
    invoiceMain.setSplitBill("NOT");
    invoiceMain.setOrderCreated(baseOrderInfo.getCreated());
    invoiceMain.setSellerNumber(baseOrganizationInfo.getStoreCode());
    invoiceMain.setSellerName(baseOrganizationInfo.getStoreName());
    invoiceMain.setIsValid(InvoiceIsValidEnum.VALID.getCode());
    invoiceMain.setCreated(new Date());
    invoiceMain.setUpdated(new Date());
    invoiceMain.setVersion(1L);

    command.setInvoiceMain(invoiceMain);
    command.setDetails(invoiceDetails);
    command.setThirdOrderNo(baseOrderInfo.getThirdOrderNo().getThirdOrderNo());
    return command;
  }


  public List<InvoiceAmount> convertByOfflineOrder(OfflineOrderModel data,
      List<OfflineRefundOrderModel> refundDataList) {
    List<InvoiceAmount> invoiceAmounts = new ArrayList<>();
    BigDecimal actualPayAmount = data.getBaseOrderInfo().getActualPayAmount();

    List<OfflineOrderModel.OrderCouponData> collect = data.getOrderCouponList().stream().filter(
        d -> d.getType().equals(DataDimensionTypeEnum.ORDER.name()) && d.getCouponType()
            .equals(CouponTypeEnum.XY_CASH.name())).collect(Collectors.toList());

    BigDecimal xy_cash = BigDecimal.ZERO;
    if (!CollectionUtils.isEmpty(collect)) {
      for (OfflineOrderModel.OrderCouponData orderCouponData : collect) {
        xy_cash = xy_cash.add(orderCouponData.getUsedCouponAmount());
      }
    }
    BigDecimal subtract = actualPayAmount.subtract(xy_cash);
    for (OfflineRefundOrderModel offlineRefundOrderModel : refundDataList) {
      subtract = subtract.subtract(offlineRefundOrderModel.getBaseRefundInfo().getConsumerRefund());
    }
    invoiceAmounts.add(new InvoiceAmount(InvoiceAmountKeyEnum.INVOICE_AMOUNT, subtract));
    return invoiceAmounts;
  }


}
