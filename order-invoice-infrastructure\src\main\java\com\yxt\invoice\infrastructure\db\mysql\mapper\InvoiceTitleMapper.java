package com.yxt.invoice.infrastructure.db.mysql.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.invoice.infrastructure.db.mysql.entity.InvoiceTitleDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 发票抬头Mapper接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-06
 */
@Mapper
public interface InvoiceTitleMapper extends BaseMapper<InvoiceTitleDO> {

    /**
     * 根据抬头单号查询
     *
     * @param invoiceTitleNo 抬头单号
     * @return 发票抬头
     */
    InvoiceTitleDO selectByInvoiceTitleNo(@Param("invoiceTitleNo") String invoiceTitleNo);

    /**
     * 根据用户ID查询有效的发票抬头列表
     *
     * @param userId 用户ID
     * @return 发票抬头列表
     */
    List<InvoiceTitleDO> selectValidByUserId(@Param("userId") String userId);

    /**
     * 根据抬头单号逻辑删除
     *
     * @param invoiceTitleNo 抬头单号
     * @param updatedBy      更新人
     * @return 影响行数
     */
    int deleteByInvoiceTitleNo(@Param("invoiceTitleNo") String invoiceTitleNo, 
                              @Param("updatedBy") String updatedBy);

    /**
     * 根据抬头单号更新
     *
     * @param invoiceTitle 发票抬头
     * @return 影响行数
     */
    int updateByInvoiceTitleNo(InvoiceTitleDO invoiceTitle);
}
