package com.yxt.invoice.infrastructure.db.mysql.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.invoice.infrastructure.db.mysql.entity.InvoiceTitleDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 发票抬头Mapper接口
 * 使用MyBatis-Plus实现，不需要XML文件
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-06
 */
@Mapper
public interface InvoiceTitleMapper extends BaseMapper<InvoiceTitleDO> {
    // 继承BaseMapper即可，所有基础CRUD操作都已包含
    // 复杂查询通过LambdaQueryWrapper在Repository实现类中完成
}
