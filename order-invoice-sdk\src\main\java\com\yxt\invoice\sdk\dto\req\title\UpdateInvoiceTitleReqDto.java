package com.yxt.invoice.sdk.dto.req.title;

import com.yxt.order.types.invoice.enums.InvoiceBuyerPartyTypeEnum;
import com.yxt.order.types.invoice.enums.InvoiceTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 编辑发票抬头请求
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-06
 */
@Data
public class UpdateInvoiceTitleReqDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 抬头内部单号
     */
    @ApiModelProperty(value = "抬头内部单号", required = true, example = "TITLE20250906001")
    @NotBlank(message = "抬头内部单号不能为空")
    @Size(max = 50, message = "抬头内部单号长度不能超过50个字符")
    private String invoiceTitleNo;

    /**
     * 发票类型
     */
    @ApiModelProperty(value = "发票类型 专票:SPECIAL_INVOICE 普票:ORDINARY_INVOICE", required = true, example = "ORDINARY_INVOICE")
    @NotNull(message = "发票类型不能为空")
    private InvoiceTypeEnum invoiceType;

    /**
     * 购方类型
     */
    @ApiModelProperty(value = "购方类型 个人:INDIVIDUAL 单位:ORGANIZATION", required = true, example = "INDIVIDUAL")
    @NotNull(message = "购方类型不能为空")
    private InvoiceBuyerPartyTypeEnum buyerPartyType;

    /**
     * 购方名字
     */
    @ApiModelProperty(value = "购方名字", required = true, example = "张三")
    @NotBlank(message = "购方名字不能为空")
    @Size(max = 50, message = "购方名字长度不能超过50个字符")
    private String buyerName;

    /**
     * 购方个人身份证单位纳税人识别号
     */
    @ApiModelProperty(value = "购方个人身份证/单位纳税人识别号", example = "110101199001011234")
    @Size(max = 50, message = "购方个人身份证/单位纳税人识别号长度不能超过50个字符")
    private String buyerTin;

    /**
     * 购方电话
     */
    @ApiModelProperty(value = "购方电话", example = "010-12345678")
    @Size(max = 50, message = "购方电话长度不能超过50个字符")
    private String buyerPhone;

    /**
     * 购方地址
     */
    @ApiModelProperty(value = "购方地址", example = "北京市朝阳区xxx街道xxx号")
    @Size(max = 100, message = "购方地址长度不能超过100个字符")
    private String buyerAddress;

    /**
     * 购方银行,专票必填
     */
    @ApiModelProperty(value = "购方银行(专票必填)", example = "中国工商银行")
    @Size(max = 50, message = "购方银行长度不能超过50个字符")
    private String buyerBank;

    /**
     * 购方银行账户,专票必填
     */
    @ApiModelProperty(value = "购方银行账户(专票必填)", example = "6222021234567890123")
    @Size(max = 50, message = "购方银行账户长度不能超过50个字符")
    private String buyerBankAccount;

    /**
     * 购方邮箱
     */
    @ApiModelProperty(value = "购方邮箱", example = "<EMAIL>")
    @Size(max = 50, message = "购方邮箱长度不能超过50个字符")
    private String buyerEmail;

    /**
     * 购方手机号
     */
    @ApiModelProperty(value = "购方手机号", example = "***********")
    @Size(max = 50, message = "购方手机号长度不能超过50个字符")
    private String buyerMobile;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人", required = true, example = "admin")
    @NotBlank(message = "操作人不能为空")
    @Size(max = 50, message = "操作人长度不能超过50个字符")
    private String operatorUserId;
}
