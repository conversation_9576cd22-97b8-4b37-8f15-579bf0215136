package com.yxt.invoice.sdk.dto.res.title;

import com.yxt.invoice.sdk.dto.InvoiceTitleDTO;
import com.yxt.lang.dto.api.ResponseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 发票抬头响应
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-06
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class InvoiceTitleResDto extends ResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 发票抬头信息
     */
    @ApiModelProperty(value = "发票抬头信息")
    private InvoiceTitleDTO invoiceTitle;
}
