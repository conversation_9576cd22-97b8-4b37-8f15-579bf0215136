package com.yxt.invoice.sdk.dto.req.title;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 发票抬头列表查询请求
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-06
 */
@Data
public class InvoiceTitleListReqDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 会员编号
     */
    @ApiModelProperty(value = "会员编号", required = true, example = "USER123456")
    @NotBlank(message = "会员编号不能为空")
    @Size(max = 50, message = "会员编号长度不能超过50个字符")
    private String userId;

    /**
     * 当前页码
     */
    @ApiModelProperty(value = "当前页码", example = "1")
    @Min(value = 1, message = "页码必须大于0")
    private Integer currentPage = 1;

    /**
     * 每页大小
     */
    @ApiModelProperty(value = "每页大小", example = "10")
    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = 100, message = "每页大小不能超过100")
    private Integer pageSize = 10;
}
