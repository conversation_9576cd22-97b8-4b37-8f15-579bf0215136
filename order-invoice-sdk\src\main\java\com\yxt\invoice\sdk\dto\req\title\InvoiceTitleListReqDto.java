package com.yxt.invoice.sdk.dto.req.title;

import com.yxt.lang.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 发票抬头列表查询请求
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-06
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class InvoiceTitleListReqDto extends PageBase implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 会员编号
     */
    @ApiModelProperty(value = "会员编号", required = true, example = "USER123456")
    @NotBlank(message = "会员编号不能为空")
    @Size(max = 50, message = "会员编号长度不能超过50个字符")
    private String userId;


}
