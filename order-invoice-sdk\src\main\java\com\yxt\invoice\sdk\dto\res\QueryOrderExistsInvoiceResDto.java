package com.yxt.invoice.sdk.dto.res;

import com.yxt.invoice.sdk.dto.InvoiceAmountDTO;
import com.yxt.invoice.sdk.dto.InvoiceMainDTO;
import com.yxt.order.types.invoice.enums.InvoiceBusinessTypeEnum;
import com.yxt.order.types.invoice.enums.InvoiceTransactionChannelEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class QueryOrderExistsInvoiceResDto implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 平台编码
     */
    @ApiModelProperty(value = "平台编码", example = "YXDJ")
    private String thirdPlatformCode;

    /**
     * 第三方平台订单号
     */
    @ApiModelProperty(value = "第三方平台订单号", example = "THIRD20250811001")
    private String thirdOrderNo;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号", example = "ORDER20250811001")
    private String orderNo;

    /**
     * pos销售单号
     */
    @ApiModelProperty(value = "POS销售单号", example = "POS20250811001")
    private String posNo;

    /**
     * 交易场景 ONLINE:线上交易, OFFLINE:线下交易
     */
    @ApiModelProperty(value = "交易场景 ONLINE:线上交易, OFFLINE:线下交易")
    private InvoiceTransactionChannelEnum transactionChannel;

    /**
     * 业务类型  O2O、B2C、B2B、OFFLINE
     */
    @ApiModelProperty("业务类型  O2O、B2C、B2B、OFFLINE")
    private InvoiceBusinessTypeEnum businessType;

    /**
     * 是否已开票
     */
    @ApiModelProperty(value = "发票信息", notes = "已申请时有值")
    private List<InvoiceMainDTO> invoiceMains;

    @ApiModelProperty(value = "发票开具金额选项", notes = "未开票时有值")
    private List<InvoiceAmountDTO> invoiceAmounts;


}
