package com.yxt.invoice.domain.command.user;

import com.yxt.invoice.domain.command.PageDateConditionCommand;
import com.yxt.order.types.invoice.enums.InvoiceRedBlueTypeEnum;
import com.yxt.order.types.invoice.enums.InvoiceStatusEnum;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户端开票列表指令
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UserQueryInvoiceListCommand extends PageDateConditionCommand {

  private String userId;

  private List<InvoiceStatusEnum> invoiceStatusList;

  private Date applyTimeStart;

  private Date applyTimeEnd;

  // 默认是蓝票
  private InvoiceRedBlueTypeEnum invoiceRedBlueType = InvoiceRedBlueTypeEnum.TAX_INVOICE;


}
