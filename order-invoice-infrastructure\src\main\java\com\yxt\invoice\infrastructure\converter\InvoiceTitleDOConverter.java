package com.yxt.invoice.infrastructure.converter;

import com.yxt.invoice.domain.model.InvoiceTitle;
import com.yxt.invoice.infrastructure.db.mysql.entity.InvoiceTitleDO;
import com.yxt.order.types.invoice.enums.InvoiceBuyerPartyTypeEnum;
import com.yxt.order.types.invoice.enums.InvoiceTypeEnum;

/**
 * 发票抬头DO转换器
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-06
 */
public class InvoiceTitleDOConverter {

    /**
     * 领域对象转DO
     */
    public static InvoiceTitleDO toInvoiceTitleDO(InvoiceTitle invoiceTitle) {
        if (invoiceTitle == null) {
            return null;
        }

        InvoiceTitleDO invoiceTitleDO = new InvoiceTitleDO();
        invoiceTitleDO.setId(invoiceTitle.getId());
        invoiceTitleDO.setInvoiceTitleNo(invoiceTitle.getInvoiceTitleNo());
        invoiceTitleDO.setUserId(invoiceTitle.getUserId());
        
        // 枚举转字符串
        if (invoiceTitle.getInvoiceType() != null) {
            invoiceTitleDO.setInvoiceType(invoiceTitle.getInvoiceType().name());
        }
        if (invoiceTitle.getBuyerPartyType() != null) {
            invoiceTitleDO.setBuyerPartyType(invoiceTitle.getBuyerPartyType().name());
        }
        
        invoiceTitleDO.setBuyerName(invoiceTitle.getBuyerName());
        invoiceTitleDO.setBuyerTin(invoiceTitle.getBuyerTin());
        invoiceTitleDO.setBuyerPhone(invoiceTitle.getBuyerPhone());
        invoiceTitleDO.setBuyerAddress(invoiceTitle.getBuyerAddress());
        invoiceTitleDO.setBuyerBank(invoiceTitle.getBuyerBank());
        invoiceTitleDO.setBuyerBankAccount(invoiceTitle.getBuyerBankAccount());
        invoiceTitleDO.setBuyerEmail(invoiceTitle.getBuyerEmail());
        invoiceTitleDO.setBuyerMobile(invoiceTitle.getBuyerMobile());
        invoiceTitleDO.setIsValid(invoiceTitle.getIsValid());
        invoiceTitleDO.setCreatedBy(invoiceTitle.getCreatedBy());
        invoiceTitleDO.setUpdatedBy(invoiceTitle.getUpdatedBy());
        invoiceTitleDO.setSysCreateTime(invoiceTitle.getSysCreateTime());
        invoiceTitleDO.setSysUpdateTime(invoiceTitle.getSysUpdateTime());
        invoiceTitleDO.setVersion(invoiceTitle.getVersion());

        return invoiceTitleDO;
    }

    /**
     * DO转领域对象
     */
    public static InvoiceTitle toInvoiceTitle(InvoiceTitleDO invoiceTitleDO) {
        if (invoiceTitleDO == null) {
            return null;
        }

        InvoiceTitle invoiceTitle = new InvoiceTitle();
        invoiceTitle.setId(invoiceTitleDO.getId());
        invoiceTitle.setInvoiceTitleNo(invoiceTitleDO.getInvoiceTitleNo());
        invoiceTitle.setUserId(invoiceTitleDO.getUserId());
        
        // 字符串转枚举
        if (invoiceTitleDO.getInvoiceType() != null) {
            invoiceTitle.setInvoiceType(InvoiceTypeEnum.valueOf(invoiceTitleDO.getInvoiceType()));
        }
        if (invoiceTitleDO.getBuyerPartyType() != null) {
            invoiceTitle.setBuyerPartyType(InvoiceBuyerPartyTypeEnum.valueOf(invoiceTitleDO.getBuyerPartyType()));
        }
        
        invoiceTitle.setBuyerName(invoiceTitleDO.getBuyerName());
        invoiceTitle.setBuyerTin(invoiceTitleDO.getBuyerTin());
        invoiceTitle.setBuyerPhone(invoiceTitleDO.getBuyerPhone());
        invoiceTitle.setBuyerAddress(invoiceTitleDO.getBuyerAddress());
        invoiceTitle.setBuyerBank(invoiceTitleDO.getBuyerBank());
        invoiceTitle.setBuyerBankAccount(invoiceTitleDO.getBuyerBankAccount());
        invoiceTitle.setBuyerEmail(invoiceTitleDO.getBuyerEmail());
        invoiceTitle.setBuyerMobile(invoiceTitleDO.getBuyerMobile());
        invoiceTitle.setIsValid(invoiceTitleDO.getIsValid());
        invoiceTitle.setCreatedBy(invoiceTitleDO.getCreatedBy());
        invoiceTitle.setUpdatedBy(invoiceTitleDO.getUpdatedBy());
        invoiceTitle.setSysCreateTime(invoiceTitleDO.getSysCreateTime());
        invoiceTitle.setSysUpdateTime(invoiceTitleDO.getSysUpdateTime());
        invoiceTitle.setVersion(invoiceTitleDO.getVersion());

        return invoiceTitle;
    }
}
