package com.yxt.invoice.domain.repository;

import com.yxt.invoice.domain.command.OrderExistsInvoiceQueryCommand;
import com.yxt.invoice.domain.command.QueryInvoiceListCommand;
import com.yxt.invoice.domain.command.user.UserQueryInvoiceListCommand;
import com.yxt.invoice.domain.model.InvoiceMain;
import com.yxt.invoice.domain.model.aggregate.InvoiceAggregate;
import com.yxt.lang.dto.api.PageDTO;
import java.util.List;

/**
 * 发票仓储接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
public interface InvoiceRepository {



  /**
   * 查询是否存在发票
   *
   * @param existsOrderInvoiceCommand
   * @return
   */
  Boolean checkExistsFromDb(OrderExistsInvoiceQueryCommand existsOrderInvoiceCommand);
  RemoteDataResult checkExistsFromRemote(OrderExistsInvoiceQueryCommand existsOrderInvoiceCommand);

  /**
   * 根据用户ID和状态查询发票（支持分页）
   *
   * @param condition 条件
   * @return 发票聚合根列表
   */
  PageDTO<InvoiceMain> pageInvoiceList(QueryInvoiceListCommand condition);


  /**
   * 保存发票聚合根
   *
   * @param aggregate 发票聚合根
   */
  void doSave(InvoiceAggregate aggregate);


  /**
   * 保存发票聚合根
   *
   * @param redInvoiceAggregate 红票聚合根
   * @return 保存后的发票聚合根
   */
  void doSaveRedInvoice(InvoiceAggregate redInvoiceAggregate);


  /**
   * 根据订单ID查询发票（用于重复申请检查）
   *
   * @param orderNo 订单号
   * @return 发票聚合根，不存在返回null
   */
  List<InvoiceAggregate> findInvoiceByOrderNo(String orderNo);
  List<InvoiceAggregate> findRedInvoiceByOrderNo(String orderNo);

  /**
   * 根据开票单号查询发票
   *
   * @param invoiceMainNo 开票单号
   * @return 发票聚合根，不存在返回null
   */
  InvoiceAggregate findByInvoiceMainNo(String invoiceMainNo);
  InvoiceAggregate findByRedInvoiceMainNo(String redInvoiceMainNo);


  PageDTO<InvoiceMain> userInvoiceList(UserQueryInvoiceListCommand command);
}
