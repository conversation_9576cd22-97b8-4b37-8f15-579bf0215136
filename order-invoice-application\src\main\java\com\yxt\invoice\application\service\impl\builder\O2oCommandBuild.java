package com.yxt.invoice.application.service.impl.builder;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Preconditions;
import com.yxt.domain.order.order_query.req.OrderSearchByOrderNoReqInvoice;
import com.yxt.domain.order.order_query.res.OrderDomainRelatedResInvoice;
import com.yxt.domain.order.refund_query.req.RefundPageSearchReq;
import com.yxt.domain.order.refund_query.req.RefundSearchByRefundNoReq;
import com.yxt.domain.order.refund_query.res.RefundDomainRelatedRes;
import com.yxt.domain.order.refund_query.res.RefundSimpleRes;
import com.yxt.invoice.application.third.baseinfo.feign.BaseInfoClient;
import com.yxt.invoice.application.third.baseinfo.feign.MemberClient;
import com.yxt.invoice.application.third.goods.dto.req.AveragePriceQuery;
import com.yxt.invoice.application.third.goods.dto.res.AveragePriceVO;
import com.yxt.invoice.application.third.goods.feign.MiddleMerchandiseClient;
import com.yxt.invoice.application.third.order.feign.OrderQueryDomainApiFeign;
import com.yxt.invoice.application.third.order.feign.RefundQueryDomainApiFeign;
import com.yxt.invoice.domain.command.ApplyInvoiceCommand;
import com.yxt.invoice.domain.model.InvoiceDetail;
import com.yxt.invoice.domain.model.InvoiceMain;
import com.yxt.invoice.domain.model.valueobject.InvoiceAmount;
import com.yxt.invoice.infrastructure.common.utils.SubUtil;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.middle.baseinfo.res.org.BizUnitOrgResDTO;
import com.yxt.middle.baseinfo.res.store.StoreInfoDataResDTO;
import com.yxt.middle.member.res.member.MemberInfoVo;
import com.yxt.order.common.base_order_dto.OrderDetail;
import com.yxt.order.common.base_order_dto.OrderInfo;
import com.yxt.order.common.base_order_dto.OrderPayInfo;
import com.yxt.order.common.base_order_dto.RefundDetail;
import com.yxt.order.types.DsConstants;
import com.yxt.order.types.invoice.enums.InvoiceAmountKeyEnum;
import com.yxt.order.types.invoice.enums.InvoiceBusinessTypeEnum;
import com.yxt.order.types.invoice.enums.InvoiceDeliveryTypeEnum;
import com.yxt.order.types.invoice.enums.InvoiceLineTypeEnum;
import com.yxt.order.types.invoice.enums.InvoiceStatusEnum;
import com.yxt.order.types.invoice.enums.InvoiceSyncStatusEnum;
import com.yxt.order.types.invoice.enums.InvoiceTransactionChannelEnum;
import com.yxt.order.types.offline.OfflineOrderNo;
import com.yxt.order.types.offline.OfflineThirdOrderNo;
import com.yxt.order.types.order.enums.ErpStateEnum;
import com.yxt.order.types.order.enums.OrderStateEnum;
import com.yxt.order.types.order.enums.PlatformCodeEnum;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
public class O2oCommandBuild extends AbstractApplyInvoiceCommandBuilder {


  @Resource
  private OrderQueryDomainApiFeign orderQueryDomainApiFeign;

  @Resource
  private RefundQueryDomainApiFeign refundQueryDomainApiFeign;


  @Resource
  private MiddleMerchandiseClient middleMerchandiseClient;

  @Resource
  private BaseInfoClient baseInfoClient;
  @Resource
  private MemberClient memberClient;

  @Override
  public Boolean route(ApplyInvoiceCommand command) {
    InvoiceMain invoiceMain = command.getInvoiceMain();
    return invoiceMain.getTransactionChannel().equals(InvoiceTransactionChannelEnum.ONLINE)
        && invoiceMain.getBusinessType().equals(InvoiceBusinessTypeEnum.O2O);
  }

  @Override
  public ApplyInvoiceCommand build(ApplyInvoiceCommand command) {

    InvoiceMain invoiceMain = command.getInvoiceMain();
    String orderNo = invoiceMain.getOrderNo().getOrderNo();
    String userId = invoiceMain.getUserId();
    String merCode = invoiceMain.getMerCode();

    OrderSearchByOrderNoReqInvoice req = new OrderSearchByOrderNoReqInvoice();
    req.setOrderNo(orderNo);
    ResponseBase<OrderDomainRelatedResInvoice> orderResponse = orderQueryDomainApiFeign.orderSearchByOrderNoInvoice(
        req);
    Preconditions.checkArgument(orderResponse.checkSuccess(), "查询线上单O2O数据异常,暂不支持开票");
    OrderDomainRelatedResInvoice data = orderResponse.getData();

    OrderInfo orderInfo = data.getOrderInfo();
    Preconditions.checkArgument(
        Objects.equals(orderInfo.getErpState(), ErpStateEnum.HAS_SALE.getCode()),
        String.format("订单下账状态 %s,暂不支持开票。需要为已下账",
            ErpStateEnum.getByErpState(orderInfo.getErpState()).getMsg()));
    Preconditions.checkArgument(
        !Objects.equals(orderInfo.getOrderState(), OrderStateEnum.CLOSED.getCode())
            && !Objects.equals(orderInfo.getOrderState(), OrderStateEnum.CANCEL.getCode()),
        String.format("订单状态 %s,暂不支持开票",
            OrderStateEnum.getOrderState(orderInfo.getOrderState()).getMsg()));
    Preconditions.checkArgument(StringUtils.isNotEmpty(orderInfo.getErpSaleNo()),
        "订单销售流水信息不全,暂不支持开票");
    RefundPageSearchReq refundPageSearchReq = new RefundPageSearchReq();
    refundPageSearchReq.setOrderNoList(Collections.singletonList(orderNo));
    ResponseBase<PageDTO<RefundSimpleRes>> pageDTOResponseBase = refundQueryDomainApiFeign.refundSearchPage(
        refundPageSearchReq);
    Preconditions.checkArgument(pageDTOResponseBase.checkSuccess(),
        "订单销售流水信息不全,暂不支持开票");

    if (StringUtils.isNotEmpty(orderInfo.getMemberNo())) {
      ResponseBase<MemberInfoVo> memberByCardNo = memberClient.getMemberByCardNo(
          orderInfo.getMemberNo());
      Preconditions.checkArgument(memberByCardNo.checkSuccess(), "查询会员信息失败,暂不支持开票");
      Preconditions.checkArgument(memberByCardNo.getData().getUserId().toString().equals(userId),
          "会员信息不一致,暂不支持开票");
    }
    List<RefundDomainRelatedRes> refundDataList = new ArrayList<>();
    List<RefundSimpleRes> tempRefundList = pageDTOResponseBase.getData().getData();
    for (RefundSimpleRes dataRefund : tempRefundList) {
      RefundSearchByRefundNoReq refundSearchByRefundNoReq = new RefundSearchByRefundNoReq();
      refundSearchByRefundNoReq.setRefundNo(dataRefund.getRefundNo());
      ResponseBase<RefundDomainRelatedRes> refundDomainRelatedResResponseBase = refundQueryDomainApiFeign.refundSearchByRefundNo(
          refundSearchByRefundNoReq);
      Preconditions.checkArgument(refundDomainRelatedResResponseBase.checkSuccess(),
          "查询退款单失败,暂不支持开票");
      RefundDomainRelatedRes dataRefundTemp = refundDomainRelatedResResponseBase.getData();
      if (dataRefundTemp.getRefundOrder().getState() == 102
          || dataRefundTemp.getRefundOrder().getState() == 103) {
        continue;
      }
      Preconditions.checkArgument(dataRefundTemp.getRefundOrder().getState() == 100,
          "退款单状态,暂不支持开票"+ dataRefundTemp.getRefundOrder().getState());
      Preconditions.checkArgument(dataRefundTemp.getRefundOrder().getErpState() == 100,
          "退款单下账状态,暂不支持开票"+dataRefundTemp.getRefundOrder().getErpState());
      refundDataList.add(dataRefundTemp);
    }
    String storeCode = orderInfo.getOrganizationCode();
    List<String> erpCodeList = data.getDetailList().stream().map(OrderDetail::getErpCode).distinct()
        .collect(Collectors.toList());
    ResponseBase<List<AveragePriceVO>> detailListPriceResponse = middleMerchandiseClient.queryAveragePrice(
        DsConstants.SYSTEM, AveragePriceQuery.buildBean(merCode, storeCode, erpCodeList));
    Preconditions.checkArgument(detailListPriceResponse.checkSuccess(),
        "查询商品均价失败,暂不支持开票");
    ResponseBase<StoreInfoDataResDTO> storeInfo = baseInfoClient.getStoreInfo(merCode, null,
        storeCode);
    Preconditions.checkArgument(storeInfo.checkSuccess(), "查询门店信息失败,暂不支持开票");
    Optional<BizUnitOrgResDTO> first = storeInfo.getData().getBizUnitOrgList().stream()
        .filter(d -> "1".equals(d.getLayer())).findFirst();
    Preconditions.checkArgument(first.isPresent(), "查询门店所属公司信息失败,暂不支持开票");
    invoiceMain.setCompanyCode(first.get().getUnitCode());
    invoiceMain.setCompanyName(first.get().getUnitName());

    return applyInvoiceConvertByOnlineOrderO2O(data, refundDataList, command,
        detailListPriceResponse.getData());
  }


  ApplyInvoiceCommand applyInvoiceConvertByOnlineOrderO2O(OrderDomainRelatedResInvoice data,
      List<RefundDomainRelatedRes> refundDataList, ApplyInvoiceCommand command,
      List<AveragePriceVO> detailPriceList) {
    OrderInfo baseOrderInfo = data.getOrderInfo();
    String deliveryTypeTemp = data.getOrderDeliveryRecord().getDeliveryType();
    InvoiceMain invoiceMain = command.getInvoiceMain();
    Date applyTime = invoiceMain.getApplyTime();
    String createdBy = invoiceMain.getCreatedBy();
    String deliveryType;
    if (deliveryTypeTemp.equals("3")) {
      deliveryType = InvoiceDeliveryTypeEnum.SELF_PICK_UP.getCode();
    } else if (deliveryTypeTemp.equals("1") || deliveryTypeTemp.equals("2")) {
      deliveryType = InvoiceDeliveryTypeEnum.PLATFORM_FULFILLMENT.getCode();
    } else {
      deliveryType = InvoiceDeliveryTypeEnum.MERCHANT_FULFILLMENT.getCode();

    }
    BigDecimal sumDetailInvoiceAmount = BigDecimal.ZERO;
    BigDecimal sumDetailTaxAmount = BigDecimal.ZERO;

    List<InvoiceAmount> invoiceAmounts = convertByOnlineOrderO2O(data, refundDataList);
    Optional<InvoiceAmount> first = invoiceAmounts.stream()
        .filter(invoiceAmount -> invoiceAmount.getKey().equals(command.getInvoiceAmount().getKey()))
        .findFirst();
    Preconditions.checkArgument(first.isPresent(), "请选择发票金额开具方式");
    BigDecimal invoiceAmount = first.get().getAmount();

    Map<String, AveragePriceVO> priceVOMap = detailPriceList.stream()
        .collect(Collectors.toMap(AveragePriceVO::getErpCode, d -> d));
    Map<String, List<RefundDetail>> mapByRefundDetailList = refundDataList.stream()
        .flatMap(model -> model.getRefundDetailList().stream())
        .collect(Collectors.groupingBy(RefundDetail::getThirdDetailId));

    Integer line = 1;

    List<InvoiceDetail> invoiceDetails = new ArrayList<>();
    for (OrderDetail orderDetail : data.getDetailList()) {
      if (orderDetail.getStatus() != 0) {
        continue;
      }
      Integer commodityCount = orderDetail.getCommodityCount();
      String rowNo = orderDetail.getThirdDetailId();
      List<RefundDetail> refundDetails = mapByRefundDetailList.get(rowNo);
      if (!CollectionUtils.isEmpty(refundDetails)) {
        for (RefundDetail refundDetail : refundDetails) {
          commodityCount = commodityCount - (refundDetail.getRefundCount());
        }
      }
      if (commodityCount <= 0) {
        continue;
      }
      AveragePriceVO averagePriceVO = priceVOMap.get(orderDetail.getErpCode());
      BigDecimal taxRate = new BigDecimal(averagePriceVO.getTaxRate());
      BigDecimal price = orderDetail.getPrice();
      BigDecimal totalAmount = price.multiply(new BigDecimal(commodityCount));   //todo 改新字段 俊峰加
      BigDecimal taxAmount = totalAmount.multiply(taxRate).setScale(2, RoundingMode.HALF_UP);
      BigDecimal PriceTaxAmount = totalAmount.add(taxAmount);

      sumDetailInvoiceAmount = sumDetailInvoiceAmount.add(totalAmount);
      sumDetailTaxAmount = sumDetailTaxAmount.add(taxAmount);

      InvoiceDetail invoiceDetail = new InvoiceDetail();
      invoiceDetail.setRowNo(rowNo);
      invoiceDetail.setLine(String.valueOf(line));
      invoiceDetail.setErpCode(orderDetail.getErpCode());
      invoiceDetail.setErpName(orderDetail.getCommodityName());
      invoiceDetail.setCommodityCount(new BigDecimal(commodityCount));
      invoiceDetail.setPrice(price);
      invoiceDetail.setTotalAmount(totalAmount);
      invoiceDetail.setTaxAmount(taxAmount);
      invoiceDetail.setTaxRate(taxRate);
      invoiceDetail.setPriceTaxAmount(PriceTaxAmount);
      invoiceDetail.setInvoiceLineType(InvoiceLineTypeEnum.REGULAR_LINE.getCode());
      invoiceDetail.setDiscountAmount(BigDecimal.ZERO);
      invoiceDetail.setPolicyStatus("NO");
      invoiceDetail.setPolicyTaxRate(null);
      invoiceDetail.setIsValid(1L);
      invoiceDetail.setCreated(applyTime);
      invoiceDetail.setUpdated(applyTime);
      invoiceDetail.setCreatedBy(createdBy);
      invoiceDetail.setUpdatedBy(createdBy);
      invoiceDetail.setVersion(1L);
      invoiceDetails.add(invoiceDetail);

      line = line + 1;
    }

    Preconditions.checkArgument(invoiceAmount.compareTo(sumDetailInvoiceAmount) != 0,
        "发票头与商品行合计金额不一致");
    BigDecimal priceTaxAmount = sumDetailInvoiceAmount.add(sumDetailTaxAmount);

    invoiceMain.setCompanyCode(command.getInvoiceMain().getCompanyCode());
    invoiceMain.setCompanyName(command.getInvoiceMain().getCompanyName());
    invoiceMain.setOrganizationCode(baseOrderInfo.getOrganizationCode());
    invoiceMain.setOrganizationName(baseOrderInfo.getOrganizationName());
    invoiceMain.setThirdPlatformCode(baseOrderInfo.getThirdPlatformCode());
    invoiceMain.setThirdOrderNo(OfflineThirdOrderNo.thirdOrderNo(baseOrderInfo.getThirdOrderNo()));
    invoiceMain.setOrderNo(OfflineOrderNo.orderNo(String.valueOf(baseOrderInfo.getOrderNo())));
    invoiceMain.setPosNo(baseOrderInfo.getErpSaleNo());
    invoiceMain.setInvoiceStatus(InvoiceStatusEnum.WAIT);
    invoiceMain.setSyncStatus(InvoiceSyncStatusEnum.WAIT);
    invoiceMain.setActualPayAmount(invoiceAmount);
    invoiceMain.setDeliveryAmount(BigDecimal.ZERO);
    invoiceMain.setDeliveryType(deliveryType);
    invoiceMain.setInvoiceAmount(invoiceAmount);
    invoiceMain.setTaxAmount(sumDetailTaxAmount);
    invoiceMain.setPriceTaxAmount(priceTaxAmount);
    invoiceMain.setSplitBill("NOT");
    invoiceMain.setOrderCreated(baseOrderInfo.getCreated());
    invoiceMain.setSellerNumber(baseOrderInfo.getOrganizationCode());
    invoiceMain.setSellerName(baseOrderInfo.getOrganizationName());
    invoiceMain.setIsValid(1L);
    invoiceMain.setCreated(new Date());
    invoiceMain.setUpdated(new Date());
    invoiceMain.setVersion(1L);

    command.setInvoiceMain(invoiceMain);
    command.setDetails(invoiceDetails);
    command.setThirdOrderNo(baseOrderInfo.getThirdOrderNo());

    return command;
  }

  public List<InvoiceAmount> convertByOnlineOrderO2O(OrderDomainRelatedResInvoice data,
      List<RefundDomainRelatedRes> refundDataList) {
    List<InvoiceAmount> invoiceAmounts = new ArrayList<>();
    String deliveryType = data.getOrderDeliveryRecord().getDeliveryType();

    BigDecimal buyerActualAmount = data.getOrderPayInfo().getBuyerActualAmount();//todo 改新字段 俊峰加

    BigDecimal xy_cash = BigDecimal.ZERO;
    // 新增现金券金额
    List<OrderPayInfo.PaySaleInfo> paySaleInfos = JSON.parseArray(
        data.getOrderPayInfo().getPaySaleInfo(), OrderPayInfo.PaySaleInfo.class);
    if (CollUtil.isNotEmpty(paySaleInfos)) {
      xy_cash = paySaleInfos.stream().map(OrderPayInfo.PaySaleInfo::getSaleAmount)
          .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add)
          .setScale(2, RoundingMode.HALF_UP);
    }

    BigDecimal subtract = buyerActualAmount.subtract(xy_cash);
    for (RefundDomainRelatedRes refundDomainRelatedRes : refundDataList) {
      subtract = subtract.subtract(refundDomainRelatedRes.getRefundOrder().getConsumerRefund());
    }

    invoiceAmounts.add(new InvoiceAmount(InvoiceAmountKeyEnum.INVOICE_AMOUNT, subtract));
    if (deliveryType.equals("3")) {
      BigDecimal invoiceAmountWithPostFee = subtract.add(data.getOrderPayInfo().getDeliveryFee()
          .subtract(data.getOrderPayInfo().getPostFeeDiscount()));
      BigDecimal invoiceAmountWithPostFeeWithSubsidy = invoiceAmountWithPostFee.add(
          data.getOrderPayInfo().getPlatformDiscount());
      if (data.getOrderInfo().getThirdPlatformCode().equals(PlatformCodeEnum.JD_DAOJIA.getCode())) {
        invoiceAmountWithPostFeeWithSubsidy = data.getErpBillInfo().getBillTotalAmount();
      }
      invoiceAmounts.add(new InvoiceAmount(InvoiceAmountKeyEnum.INVOICE_AMOUNT_WITH_POST_FEE,
          invoiceAmountWithPostFee));
      invoiceAmounts.add(
          new InvoiceAmount(InvoiceAmountKeyEnum.INVOICE_AMOUNT_WITH_POST_FEE_WITH_SUBSIDY,
              invoiceAmountWithPostFeeWithSubsidy));
    }
    if (deliveryType.equals("1") || deliveryType.equals("2")) {
      BigDecimal invoiceAmountWithSubsidy = subtract.add(
          data.getOrderPayInfo().getPlatformDiscount());
      invoiceAmounts.add(new InvoiceAmount(InvoiceAmountKeyEnum.INVOICE_AMOUNT_WITH_SUBSIDY,
          invoiceAmountWithSubsidy));
    }
    return invoiceAmounts;
  }


}
