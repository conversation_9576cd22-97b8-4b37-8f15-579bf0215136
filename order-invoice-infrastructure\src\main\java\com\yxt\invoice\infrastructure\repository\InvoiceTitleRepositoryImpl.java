package com.yxt.invoice.infrastructure.repository;

import com.yxt.invoice.domain.model.InvoiceTitle;
import com.yxt.invoice.domain.repository.InvoiceTitleRepository;
import com.yxt.invoice.infrastructure.converter.InvoiceTitleDOConverter;
import com.yxt.invoice.infrastructure.db.mysql.entity.InvoiceTitleDO;
import com.yxt.invoice.infrastructure.db.mysql.mapper.InvoiceTitleMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 发票抬头仓储实现
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-06
 */
@Repository
@Slf4j
public class InvoiceTitleRepositoryImpl implements InvoiceTitleRepository {

    @Resource
    private InvoiceTitleMapper invoiceTitleMapper;

    @Override
    public InvoiceTitle save(InvoiceTitle invoiceTitle) {
        log.info("保存发票抬头，抬头单号：{}", invoiceTitle.getInvoiceTitleNo());
        
        InvoiceTitleDO invoiceTitleDO = InvoiceTitleDOConverter.toInvoiceTitleDO(invoiceTitle);
        
        if (invoiceTitle.getId() == null) {
            // 新增
            invoiceTitleMapper.insert(invoiceTitleDO);
            invoiceTitle.setId(invoiceTitleDO.getId());
        } else {
            // 更新
            invoiceTitleMapper.updateById(invoiceTitleDO);
        }
        
        return invoiceTitle;
    }

    @Override
    public InvoiceTitle findByInvoiceTitleNo(String invoiceTitleNo) {
        log.info("根据抬头单号查询发票抬头，抬头单号：{}", invoiceTitleNo);
        
        InvoiceTitleDO invoiceTitleDO = invoiceTitleMapper.selectByInvoiceTitleNo(invoiceTitleNo);
        if (invoiceTitleDO == null) {
            return null;
        }
        
        return InvoiceTitleDOConverter.toInvoiceTitle(invoiceTitleDO);
    }

    @Override
    public List<InvoiceTitle> findValidByUserId(String userId) {
        log.info("根据用户ID查询有效的发票抬头列表，用户ID：{}", userId);
        
        List<InvoiceTitleDO> invoiceTitleDOList = invoiceTitleMapper.selectValidByUserId(userId);
        
        return invoiceTitleDOList.stream()
                .map(InvoiceTitleDOConverter::toInvoiceTitle)
                .collect(Collectors.toList());
    }

    @Override
    public boolean deleteByInvoiceTitleNo(String invoiceTitleNo, String updatedBy) {
        log.info("根据抬头单号逻辑删除发票抬头，抬头单号：{}，操作人：{}", invoiceTitleNo, updatedBy);
        
        int result = invoiceTitleMapper.deleteByInvoiceTitleNo(invoiceTitleNo, updatedBy);
        return result > 0;
    }

    @Override
    public InvoiceTitle update(InvoiceTitle invoiceTitle) {
        log.info("更新发票抬头，抬头单号：{}", invoiceTitle.getInvoiceTitleNo());
        
        InvoiceTitleDO invoiceTitleDO = InvoiceTitleDOConverter.toInvoiceTitleDO(invoiceTitle);
        invoiceTitleDO.setSysUpdateTime(new Date());
        
        int result = invoiceTitleMapper.updateByInvoiceTitleNo(invoiceTitleDO);
        if (result > 0) {
            return invoiceTitle;
        }
        
        throw new RuntimeException("更新发票抬头失败，抬头单号：" + invoiceTitle.getInvoiceTitleNo());
    }
}
