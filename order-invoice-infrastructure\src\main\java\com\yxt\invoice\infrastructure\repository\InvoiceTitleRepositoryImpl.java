package com.yxt.invoice.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.invoice.domain.model.InvoiceTitle;
import com.yxt.invoice.domain.repository.InvoiceTitleRepository;
import com.yxt.invoice.infrastructure.converter.InvoiceTitleDOConverter;
import com.yxt.invoice.infrastructure.db.mysql.entity.InvoiceTitleDO;
import com.yxt.invoice.infrastructure.db.mysql.mapper.InvoiceTitleMapper;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.order.types.invoice.enums.InvoiceIsValidEnum;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * 发票抬头仓储实现
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-06
 */
@Repository
@Slf4j
public class InvoiceTitleRepositoryImpl implements InvoiceTitleRepository {

  @Resource
  private InvoiceTitleMapper invoiceTitleMapper;

  @Override
  public InvoiceTitle save(InvoiceTitle invoiceTitle) {
    log.info("保存发票抬头，抬头单号：{}", invoiceTitle.getInvoiceTitleNo());

    InvoiceTitleDO invoiceTitleDO = InvoiceTitleDOConverter.toInvoiceTitleDO(invoiceTitle);

    if (invoiceTitle.getId() == null) {
      // 新增
      invoiceTitleMapper.insert(invoiceTitleDO);
      invoiceTitle.setId(invoiceTitleDO.getId());
    } else {
      // 更新
      invoiceTitleMapper.updateById(invoiceTitleDO);
    }

    return invoiceTitle;
  }

  @Override
  public InvoiceTitle findByInvoiceTitleNo(String invoiceTitleNo) {
    log.info("根据抬头单号查询发票抬头，抬头单号：{}", invoiceTitleNo);

    LambdaQueryWrapper<InvoiceTitleDO> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(InvoiceTitleDO::getInvoiceTitleNo, invoiceTitleNo);
    wrapper.eq(InvoiceTitleDO::getIsValid, InvoiceIsValidEnum.VALID.getCode());

    InvoiceTitleDO invoiceTitleDO = invoiceTitleMapper.selectOne(wrapper);
    if (invoiceTitleDO == null) {
      throw new RuntimeException("发票抬头不存在");
    }

    return InvoiceTitleDOConverter.toInvoiceTitle(invoiceTitleDO);
  }

  @Override
  public PageDTO<InvoiceTitle> pageInvoiceTitle(String userId, Long page, Long pageSize) {
    log.info("根据用户ID查询有效的发票抬头列表，用户ID：{}, 页码：{}, 页大小：{}", userId, page, pageSize);

    LambdaQueryWrapper<InvoiceTitleDO> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(InvoiceTitleDO::getUserId, userId)
        .eq(InvoiceTitleDO::getIsValid, InvoiceIsValidEnum.VALID.getCode())
        .orderByDesc(InvoiceTitleDO::getSysCreateTime);

    // 执行分页查询
    IPage<InvoiceTitleDO> iPage = new Page<>(page, pageSize);
    IPage<InvoiceTitleDO> pageResult = invoiceTitleMapper.selectPage(iPage, wrapper);

    // 转换为领域对象
    List<InvoiceTitle> invoiceTitleList = pageResult.getRecords().stream()
        .map(InvoiceTitleDOConverter::toInvoiceTitle)
        .collect(Collectors.toList());

    PageDTO<InvoiceTitle> pageDTO = new PageDTO<>();
    pageDTO.setTotalCount(pageResult.getTotal());
    pageDTO.setTotalPage(pageResult.getPages());
    pageDTO.setData(invoiceTitleList);
    pageDTO.setCurrentPage(page);
    pageDTO.setPageSize(pageSize);

    log.info("查询发票抬头列表完成，总记录数：{}, 总页数：{}, 当前页记录数：{}",
        pageResult.getTotal(), pageResult.getPages(), invoiceTitleList.size());

    return pageDTO;
  }

  @Override
  public boolean deleteByInvoiceTitleNo(String invoiceTitleNo, String updatedBy) {
    LambdaUpdateWrapper<InvoiceTitleDO> wrapper = new LambdaUpdateWrapper<>();
    wrapper.eq(InvoiceTitleDO::getInvoiceTitleNo, invoiceTitleNo)
        .set(InvoiceTitleDO::getIsValid, -1L).set(InvoiceTitleDO::getUpdatedBy, updatedBy)
        .set(InvoiceTitleDO::getSysUpdateTime, new Date());

    int result = invoiceTitleMapper.update(null, wrapper);
    return result > 0;
  }

  @Override
  public InvoiceTitle update(InvoiceTitle invoiceTitle) {
    InvoiceTitleDO invoiceTitleDO = InvoiceTitleDOConverter.toInvoiceTitleDO(invoiceTitle);
    invoiceTitleDO.setSysUpdateTime(new Date());

    LambdaUpdateWrapper<InvoiceTitleDO> wrapper = new LambdaUpdateWrapper<>();
    wrapper.eq(InvoiceTitleDO::getInvoiceTitleNo, invoiceTitle.getInvoiceTitleNo());

    int result = invoiceTitleMapper.update(invoiceTitleDO, wrapper);
    if (result > 0) {
      return invoiceTitle;
    }

    throw new RuntimeException("更新发票抬头失败，抬头单号：" + invoiceTitle.getInvoiceTitleNo());
  }
}
