package com.yxt.invoice.application.service.impl.exists_invoice_query_third;

import com.yxt.invoice.application.third.order.feign.OfflineOrderAtomForInvoiceApiFeign;
import com.yxt.invoice.domain.command.OrderExistsInvoiceQueryCommand;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.sdk.offline_order.req.manage.OfflineOrderListReq;
import com.yxt.order.atom.sdk.offline_order.res.manage.OfflineOrderRes;
import com.yxt.order.types.invoice.enums.InvoiceBusinessTypeEnum;
import com.yxt.order.types.offline.OfflineThirdOrderNo;
import javax.annotation.Resource;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Component
@Order(1)
public class ExistInvoiceOfflineThird extends AbstractExistsInvoiceQueryThird {

  @Resource
  private OfflineOrderAtomForInvoiceApiFeign offlineOrderAtomForInvoiceApiFeign;

  @Override
  public OrderExistsInvoiceQueryCommand build(String thirdOrderNo) {
    OrderExistsInvoiceQueryCommand queryCommand = null;
    OfflineOrderListReq queryReqDto = new OfflineOrderListReq();
    queryReqDto.setThirdOrderNo(thirdOrderNo);
    ResponseBase<PageDTO<OfflineOrderRes>> pageDTOResponseBase = offlineOrderAtomForInvoiceApiFeign.invoiceOfflineOrderList(
        queryReqDto);
    if (pageDTOResponseBase.checkSuccess() && null != pageDTOResponseBase.getData()
        && !pageDTOResponseBase.getData().getData().isEmpty()) {
      OfflineOrderRes iOfflineOrderRes = pageDTOResponseBase.getData().getData().get(0);
      queryCommand = new OrderExistsInvoiceQueryCommand();
      queryCommand.setOrderNo(iOfflineOrderRes.getOrderNo());
      queryCommand.setBusinessType(InvoiceBusinessTypeEnum.OFFLINE);
      queryCommand.setThirdPlatformCode(iOfflineOrderRes.getThirdPlatformCode());
      queryCommand.setThirdOrderNo(
          OfflineThirdOrderNo.thirdOrderNo(iOfflineOrderRes.getThirdOrderNo()));
      queryCommand.setStoreCode(iOfflineOrderRes.getStoreCode());
    }

    return queryCommand;
  }
}
