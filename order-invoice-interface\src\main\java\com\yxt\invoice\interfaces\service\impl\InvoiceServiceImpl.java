package com.yxt.invoice.interfaces.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.google.common.collect.Lists;
import com.yxt.invoice.application.service.InvoiceApplicationService;
import com.yxt.invoice.domain.command.ApplyInvoiceCommand;
import com.yxt.invoice.domain.command.ExistsThirdOrderInvoiceCommand;
import com.yxt.invoice.domain.command.OrderExistsInvoiceQueryCommand;
import com.yxt.invoice.domain.command.QueryInvoiceDetailCommand;
import com.yxt.invoice.domain.command.QueryInvoiceListCommand;
import com.yxt.invoice.domain.command.RedCreditInvoiceCommand;
import com.yxt.invoice.domain.command.user.UserQueryInvoiceListCommand;
import com.yxt.invoice.domain.event.callback.CallbackInvoiceDetail;
import com.yxt.invoice.domain.event.callback.CallbackRedInvoiceDetail;
import com.yxt.invoice.domain.model.InvoiceMain;
import com.yxt.invoice.domain.model.aggregate.InvoiceAggregate;
import com.yxt.invoice.domain.model.valueobject.ExistsOrderInvoice;
import com.yxt.invoice.domain.repository.InvoiceRepository;
import com.yxt.invoice.infrastructure.common.configration.RemoteProperties;
import com.yxt.invoice.infrastructure.common.configration.RemoteProperties.TaxCloud;
import com.yxt.invoice.infrastructure.provider.dto.req.invoice.InvoiceDetailRequest;
import com.yxt.invoice.infrastructure.provider.dto.res.detail.invoice.InvoiceDetailResponse;
import com.yxt.invoice.infrastructure.provider.dto.res.detail.invoice.InvoiceDetailResponseData;
import com.yxt.invoice.infrastructure.provider.dto.res.detail.red_invoice.RedInvoiceDetailResponse;
import com.yxt.invoice.infrastructure.provider.dto.res.detail.red_invoice.RedInvoiceDetailResponseData;
import com.yxt.invoice.infrastructure.provider.feign.TaxCloudFeign;
import com.yxt.invoice.interfaces.converter.InvoiceDTOConverter;
import com.yxt.invoice.interfaces.service.InvoiceService;
import com.yxt.invoice.sdk.dto.InvoiceMainDTO;
import com.yxt.invoice.sdk.dto.req.UserInvoiceListReq;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.util.JsonUtils;
import com.yxt.order.types.invoice.enums.InvoiceStatusEnum;
import com.yxt.order.types.invoice.remote.RemoteInvoiceTag;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 发票服务实现 Service层，使用Command传参，委托给Application层处理
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@Service
@Slf4j
public class InvoiceServiceImpl implements InvoiceService {

  @Resource
  private InvoiceRepository invoiceRepository;

  @Resource
  private TaxCloudFeign taxCloudFeign;


  @Resource
  private RemoteProperties remoteProperties;


  @Resource
  private InvoiceApplicationService invoiceApplicationService;


  @Override
  public String applyInvoice(ApplyInvoiceCommand command) {
    command.validateApplyInvoice();
    return invoiceApplicationService.applyInvoice(command);
  }

  @Override
  public String applyRedInvoice(RedCreditInvoiceCommand command) {
    return invoiceApplicationService.applyRedInvoice(command);
  }

  @Override
  public ExistsOrderInvoice queryOrderExistsInvoice(OrderExistsInvoiceQueryCommand command) {
    return invoiceApplicationService.queryOrderExistsInvoice(command);
  }

  @Override
  public ExistsOrderInvoice queryThirdOrderExistsInvoiceReqDto(
      ExistsThirdOrderInvoiceCommand command) {
    return invoiceApplicationService.queryThirdOrderExistsInvoiceReqDto(command);
  }

  @Override
  public PageDTO<InvoiceMain> pageInvoiceList(QueryInvoiceListCommand query) {
    return invoiceApplicationService.pageInvoiceList(query);
  }

  @Override
  public InvoiceAggregate queryInvoiceDetail(QueryInvoiceDetailCommand query) {
    return invoiceApplicationService.queryInvoiceDetail(query);
  }


  @Override
  public void updateInvoiceFromOrderDetail(String remoteResponseId, String outRequestCode,
      String remoteInvoiceTag) {

    TaxCloud taxCloud = remoteProperties.getTaxCloud();
    String pId = taxCloud.getPId();
    String pSecret = taxCloud.getPSecret();

    InvoiceDetailRequest req = new InvoiceDetailRequest();
    req.setPId(pId);
    req.setPSecret(pSecret);
    req.setResponseId(remoteResponseId);
    req.setOutRequestCode(outRequestCode);
    req.setInvoiceTag(remoteInvoiceTag);

    if (RemoteInvoiceTag.BLUE_INVOICE.getCode().equals(remoteInvoiceTag)) {
      updateInvoice(req);
    } else if (RemoteInvoiceTag.RED_INVOICE.getCode().equals(remoteInvoiceTag)) {
      updateRedInvoice(req);
    } else {
      throw new RuntimeException("暂不支持该业务场景");
    }
  }

  private void updateInvoice(InvoiceDetailRequest req) {
    String invoiceMainNo = req.getOutRequestCode();
    InvoiceDetailResponse res = taxCloudFeign.queryInvoice(req);
    if (!res.success()) {
      log.error("mq回调查询详情接口失败,{}", JsonUtils.toJson(req));
      return;
    }

    InvoiceDetailResponseData data = res.getData();
    CallbackInvoiceDetail callbackInvoiceDetail = BeanUtil.toBean(data,
        CallbackInvoiceDetail.class);
    InvoiceAggregate invoiceAggregate = invoiceRepository.findByInvoiceMainNo(invoiceMainNo);
    invoiceAggregate.callBack(callbackInvoiceDetail);
    invoiceRepository.doSave(invoiceAggregate);


  }

  private void updateRedInvoice(InvoiceDetailRequest req) {
    String invoiceMainNo = req.getOutRequestCode();
    RedInvoiceDetailResponse res = taxCloudFeign.queryRedInvoice(req);
    RedInvoiceDetailResponseData data = res.getData();
    CallbackRedInvoiceDetail redInvoiceMsgModel = BeanUtil.toBean(data,
        CallbackRedInvoiceDetail.class);
    InvoiceAggregate redAggregate = invoiceRepository.findByInvoiceMainNo(invoiceMainNo);
    redAggregate.redCallBack(redInvoiceMsgModel);
    invoiceRepository.doSave(redAggregate);
  }

  @Override
  public PageDTO<InvoiceMainDTO> userInvoiceList(UserInvoiceListReq req) {

    UserQueryInvoiceListCommand command = new UserQueryInvoiceListCommand();
    command.setUserId(req.getUserId());
    command.setApplyTimeStart(req.getApplyStartDate());
    command.setApplyTimeEnd(req.getApplyEndDate());
    command.setCurrentPage(req.getCurrentPage());
    command.setPageSize(req.getPageSize());
    switch (req.getQueryInvoiceStatus()) {
      case INVOICING:
        command.setInvoiceStatusList(Lists.newArrayList(InvoiceStatusEnum.PROGRESSING));
        break;
      case INVOICED:
        command.setInvoiceStatusList(Lists.newArrayList(InvoiceStatusEnum.SUCCESS));
        break;
      default:
        command.setInvoiceStatusList(Lists.newArrayList());
    }

    PageDTO<InvoiceMain> pageDTO = invoiceApplicationService.userInvoiceList(command);

    PageDTO<InvoiceMainDTO> pageData = new PageDTO<>(req.getCurrentPage(), req.getPageSize());
    pageData.setTotalCount(pageDTO.getTotalCount());
    pageData.setTotalPage(pageDTO.getTotalPage());
    pageData.setData(pageDTO.getData().stream().map(InvoiceDTOConverter::convertToInvoiceMainDTO)
        .collect(Collectors.toList()));

    return pageData;
  }
}
