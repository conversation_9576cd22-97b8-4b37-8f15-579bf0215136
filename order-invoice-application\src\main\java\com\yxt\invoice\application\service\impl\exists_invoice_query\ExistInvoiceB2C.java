package com.yxt.invoice.application.service.impl.exists_invoice_query;

import com.google.common.base.Preconditions;
import com.yxt.domain.order.order_query.req.B2cOrderAllDetailReq;
import com.yxt.domain.order.order_query.res.B2cOmsOrderInfo;
import com.yxt.domain.order.order_query.res.B2cOrderAllDetailRes;
import com.yxt.domain.order.refund_query.req.B2cRefundAllDetailReq;
import com.yxt.domain.order.refund_query.req.B2cRefundPageSearchReq;
import com.yxt.domain.order.refund_query.res.B2cRefundAllDetailRes;
import com.yxt.domain.order.refund_query.res.B2cRefundOrderInfo;
import com.yxt.domain.order.refund_query.res.B2cRefundSimpleRes;
import com.yxt.invoice.application.service.impl.builder.B2cCommandBuild;
import com.yxt.invoice.application.service.impl.builder.O2oCommandBuild;
import com.yxt.invoice.application.service.impl.builder.OfflineCommandBuild;
import com.yxt.invoice.application.third.order.feign.B2COrderQueryDomainApiFeign;
import com.yxt.invoice.application.third.order.feign.B2CRefundQueryDomainApiFeign;
import com.yxt.invoice.application.third.order.feign.OfflineOrderAtomForInvoiceApiFeign;
import com.yxt.invoice.application.third.order.feign.OfflineOrderQueryApiFeign;
import com.yxt.invoice.application.third.order.feign.OrderQueryDomainApiFeign;
import com.yxt.invoice.application.third.order.feign.RefundQueryDomainApiFeign;
import com.yxt.invoice.domain.command.OrderExistsInvoiceQueryCommand;
import com.yxt.invoice.domain.factory.InvoiceAggregateFactory;
import com.yxt.invoice.domain.model.valueobject.ExistsOrderInvoice;
import com.yxt.invoice.domain.model.valueobject.InvoiceAmount;
import com.yxt.invoice.domain.repository.InvoiceRepository;
import com.yxt.invoice.domain.repository.ProviderInvoiceRepository;
import com.yxt.invoice.infrastructure.message.InvoiceDomainEventProducer;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.types.invoice.enums.InvoiceBusinessTypeEnum;
import com.yxt.order.types.invoice.enums.InvoiceTransactionChannelEnum;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class ExistInvoiceB2C extends AbstractExistsInvoiceQuery {

  @Resource
  private B2cCommandBuild b2cCommandBuild;

  @Resource
  private B2CRefundQueryDomainApiFeign b2CRefundQueryDomainApiFeign;

  @Resource
  private B2COrderQueryDomainApiFeign b2COrderQueryDomainApiFeign;


  @Override
  public Boolean route(InvoiceBusinessTypeEnum businessTypeEnum) {
    return InvoiceBusinessTypeEnum.B2C.equals(businessTypeEnum);
  }

  @Override
  public ExistsOrderInvoice build(OrderExistsInvoiceQueryCommand command) {
    String orderNo = command.getOrderNo();

    ExistsOrderInvoice existsOrderInvoice = new ExistsOrderInvoice();

    B2cOrderAllDetailReq req = new B2cOrderAllDetailReq();
    req.setOmsOrderNo(orderNo);
    ResponseBase<B2cOrderAllDetailRes> allDetailResResponseBase = b2COrderQueryDomainApiFeign.orderSearchByOrderNo(
        req);
    Preconditions.checkArgument(allDetailResResponseBase.checkSuccess(),
        "查询线上单B2C数据异常,暂不支持开票," + allDetailResResponseBase.getMsg());
    B2cOrderAllDetailRes allDetailRes = allDetailResResponseBase.getData();
    B2cOmsOrderInfo orderInfo = allDetailRes.getOmsOrderInfo();

    Preconditions.checkArgument(orderInfo.getErpStatus() == 100, "订单下账状态,暂不支持开票");
    Preconditions.checkArgument(
        orderInfo.getOrderStatus() != 101 && orderInfo.getOrderStatus() != 102,
        orderInfo.getOrderNo()+"订单状态,暂不支持开票"+orderInfo.getOrderStatus());
    B2cRefundPageSearchReq refundPageSearchReq = new B2cRefundPageSearchReq();
    refundPageSearchReq.setThirdOrderNos(
        Collections.singletonList(orderInfo.getThirdOrderNo()));
    ResponseBase<PageDTO<B2cRefundSimpleRes>> pageDTOResponseBase = b2CRefundQueryDomainApiFeign.refundSearchPage(
        refundPageSearchReq);
    Preconditions.checkArgument(pageDTOResponseBase.checkSuccess(),
        "订单销售流水信息不全,暂不支持开票" + orderInfo.getOrderNo());

    List<B2cRefundAllDetailRes> refundDataList = new ArrayList<>();
    List<B2cRefundSimpleRes> tempRefundList = pageDTOResponseBase.getData().getData();
    for (B2cRefundSimpleRes dataRefund : tempRefundList) {
      B2cRefundAllDetailReq refundSearchByRefundNoReq = new B2cRefundAllDetailReq();
      refundSearchByRefundNoReq.setRefundNo(String.valueOf(dataRefund.getRefundNo()));
      ResponseBase<B2cRefundAllDetailRes> refundSearchByRefundNoResResponseBase = b2CRefundQueryDomainApiFeign.refundSearchByRefundNo(
          refundSearchByRefundNoReq);
      Preconditions.checkArgument(refundSearchByRefundNoResResponseBase.checkSuccess(),
          "查询退款单失败,暂不支持开票");
      B2cRefundAllDetailRes refundAllDetailRes = refundSearchByRefundNoResResponseBase.getData();
      B2cRefundOrderInfo dataRefundTemp = refundAllDetailRes.getRefundOrderInfo();
      if (Objects.equals(dataRefundTemp.getState(), 102) || Objects.equals(
          dataRefundTemp.getState(), 103)) {
        continue;
      }
      Preconditions.checkArgument(Objects.equals(dataRefundTemp.getState(), 100),
          "退款单状态,暂不支持开票" + dataRefundTemp.getState());
      Preconditions.checkArgument(dataRefundTemp.getErpState() == 100,
          "退款单下账状态,暂不支持开票" + dataRefundTemp.getErpState());
      refundDataList.add(refundAllDetailRes);
    }
    List<InvoiceAmount> invoiceAmountList = b2cCommandBuild.convertByOnlineOrderB2C(
        allDetailRes, refundDataList);
    existsOrderInvoice.setInvoiceAmounts(invoiceAmountList);
    existsOrderInvoice.setThirdPlatformCode(orderInfo.getThirdPlatformCode());
    existsOrderInvoice.setThirdOrderNo(orderInfo.getThirdOrderNo());
    existsOrderInvoice.setOrderNo(String.valueOf(orderInfo.getOrderNo()));
    existsOrderInvoice.setPosNo(orderInfo.getErpSaleNo());
    existsOrderInvoice.setTransactionChannel(InvoiceTransactionChannelEnum.ONLINE);
    existsOrderInvoice.setBusinessType(InvoiceBusinessTypeEnum.B2C);
    return existsOrderInvoice;
  }

}
