package com.yxt.invoice.sdk.dto.req;

import com.yxt.order.types.invoice.enums.InvoiceBusinessTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import java.util.Objects;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class SyncInvoiceFromRemoteReq {

  @ApiModelProperty(value = "业务单号,可能是O2O、B2C、OFFLINE,由系统自动判断")
  @NotEmpty
  private String businessNo;

  @ApiModelProperty(value = "业务类型,不支持B2B")
  @NotNull
  private InvoiceBusinessTypeEnum businessType;


  public void valid() {
    if (Objects.nonNull(this.businessType) && InvoiceBusinessTypeEnum.B2B.equals(
        this.businessType)) {
      throw new RuntimeException("不支持B2B开票");
    }
  }

}
