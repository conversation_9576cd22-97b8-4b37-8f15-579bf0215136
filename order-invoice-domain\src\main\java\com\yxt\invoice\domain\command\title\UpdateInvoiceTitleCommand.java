package com.yxt.invoice.domain.command.title;

import com.yxt.order.types.invoice.enums.InvoiceBuyerPartyTypeEnum;
import com.yxt.order.types.invoice.enums.InvoiceTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 编辑发票抬头命令
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-06
 */
@Data
public class UpdateInvoiceTitleCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 抬头内部单号
     */
    @NotBlank(message = "抬头内部单号不能为空")
    @Size(max = 50, message = "抬头内部单号长度不能超过50个字符")
    private String invoiceTitleNo;

    /**
     * 发票类型
     */
    @NotNull(message = "发票类型不能为空")
    private InvoiceTypeEnum invoiceType;

    /**
     * 购方类型
     */
    @NotNull(message = "购方类型不能为空")
    private InvoiceBuyerPartyTypeEnum buyerPartyType;

    /**
     * 购方名字
     */
    @NotBlank(message = "购方名字不能为空")
    @Size(max = 50, message = "购方名字长度不能超过50个字符")
    private String buyerName;

    /**
     * 购方个人身份证单位纳税人识别号
     */
    @Size(max = 50, message = "购方个人身份证/单位纳税人识别号长度不能超过50个字符")
    private String buyerTin;

    /**
     * 购方电话
     */
    @Size(max = 50, message = "购方电话长度不能超过50个字符")
    private String buyerPhone;

    /**
     * 购方地址
     */
    @Size(max = 100, message = "购方地址长度不能超过100个字符")
    private String buyerAddress;

    /**
     * 购方银行,专票必填
     */
    @Size(max = 50, message = "购方银行长度不能超过50个字符")
    private String buyerBank;

    /**
     * 购方银行账户,专票必填
     */
    @Size(max = 50, message = "购方银行账户长度不能超过50个字符")
    private String buyerBankAccount;

    /**
     * 购方邮箱
     */
    @Size(max = 50, message = "购方邮箱长度不能超过50个字符")
    private String buyerEmail;

    /**
     * 购方手机号
     */
    @Size(max = 50, message = "购方手机号长度不能超过50个字符")
    private String buyerMobile;

    /**
     * 操作人
     */
    @NotBlank(message = "操作人不能为空")
    @Size(max = 50, message = "操作人长度不能超过50个字符")
    private String operatorUserId;
}
