package com.yxt.invoice.sdk.dto;

import com.yxt.order.types.invoice.enums.InvoiceBuyerPartyTypeEnum;
import com.yxt.order.types.invoice.enums.InvoiceTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 发票抬头DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-06
 */
@Data
public class InvoiceTitleDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    private Long id;

    /**
     * 抬头内部单号
     */
    @ApiModelProperty(value = "抬头内部单号", example = "TITLE20250906001")
    private String invoiceTitleNo;

    /**
     * 会员编号
     */
    @ApiModelProperty(value = "会员编号", example = "USER123456")
    private String userId;

    /**
     * 发票类型
     */
    @ApiModelProperty(value = "发票类型 专票:SPECIAL_INVOICE 普票:ORDINARY_INVOICE", example = "ORDINARY_INVOICE")
    private InvoiceTypeEnum invoiceType;

    /**
     * 购方类型
     */
    @ApiModelProperty(value = "购方类型 个人:INDIVIDUAL 单位:ORGANIZATION", example = "INDIVIDUAL")
    private InvoiceBuyerPartyTypeEnum buyerPartyType;

    /**
     * 购方名字
     */
    @ApiModelProperty(value = "购方名字", example = "张三")
    private String buyerName;

    /**
     * 购方个人身份证单位纳税人识别号
     */
    @ApiModelProperty(value = "购方个人身份证/单位纳税人识别号", example = "1101011***********")
    private String buyerTin;

    /**
     * 购方电话
     */
    @ApiModelProperty(value = "购方电话", example = "010-********")
    private String buyerPhone;

    /**
     * 购方地址
     */
    @ApiModelProperty(value = "购方地址", example = "北京市朝阳区xxx街道xxx号")
    private String buyerAddress;

    /**
     * 购方银行,专票必填
     */
    @ApiModelProperty(value = "购方银行(专票必填)", example = "中国工商银行")
    private String buyerBank;

    /**
     * 购方银行账户,专票必填
     */
    @ApiModelProperty(value = "购方银行账户(专票必填)", example = "622202********90123")
    private String buyerBankAccount;

    /**
     * 购方邮箱
     */
    @ApiModelProperty(value = "购方邮箱", example = "<EMAIL>")
    private String buyerEmail;

    /**
     * 购方手机号
     */
    @ApiModelProperty(value = "购方手机号", example = "***********")
    private String buyerMobile;

    /**
     * 是否起效
     */
    @ApiModelProperty(value = "是否起效 1-起效 -1-未起效", example = "1")
    private Long isValid;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", example = "admin")
    private String createdBy;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人", example = "admin")
    private String updatedBy;

    /**
     * 系统创建时间
     */
    @ApiModelProperty(value = "系统创建时间")
    private Date sysCreateTime;

    /**
     * 系统更新时间
     */
    @ApiModelProperty(value = "系统更新时间")
    private Date sysUpdateTime;

    /**
     * 数据版本
     */
    @ApiModelProperty(value = "数据版本", example = "1")
    private Long version;
}
