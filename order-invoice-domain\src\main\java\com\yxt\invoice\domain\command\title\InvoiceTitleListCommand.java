package com.yxt.invoice.domain.command.title;

import java.io.Serializable;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import lombok.Data;

/**
 * 发票抬头列表查询命令
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-06
 */
@Data
public class InvoiceTitleListCommand implements Serializable {

  private static final long serialVersionUID = 1L;

  /**
   * 会员编号
   */
  @NotBlank(message = "会员编号不能为空")
  @Size(max = 50, message = "会员编号长度不能超过50个字符")
  private String userId;

  /**
   * 当前页码
   */
  @Min(value = 1, message = "页码必须大于0")
  private Long currentPage = 1L;

  /**
   * 每页大小
   */
  @Min(value = 1, message = "每页大小必须大于0")
  @Max(value = 100, message = "每页大小不能超过100")
  private Long pageSize = 10L;
}
