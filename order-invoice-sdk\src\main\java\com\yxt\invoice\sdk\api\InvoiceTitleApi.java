package com.yxt.invoice.sdk.api;

import com.yxt.invoice.sdk.dto.InvoiceTitleDTO;
import com.yxt.invoice.sdk.dto.req.title.CreateInvoiceTitleReqDto;
import com.yxt.invoice.sdk.dto.req.title.DeleteInvoiceTitleReqDto;
import com.yxt.invoice.sdk.dto.req.title.InvoiceTitleDetailReqDto;
import com.yxt.invoice.sdk.dto.req.title.InvoiceTitleListReqDto;
import com.yxt.invoice.sdk.dto.req.title.UpdateInvoiceTitleReqDto;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * 发票抬头管理API
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-06
 */
public interface InvoiceTitleApi {

    /**
     * 新增发票抬头
     *
     * @param request 新增请求
     * @return 抬头单号
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增发票抬头")
    ResponseBase<String> create(@Valid @RequestBody CreateInvoiceTitleReqDto request);

    /**
     * 编辑发票抬头
     *
     * @param request 编辑请求
     * @return 操作结果
     */
    @PostMapping("/update")
    @ApiOperation(value = "编辑发票抬头")
    ResponseBase<Void> update(@Valid @RequestBody UpdateInvoiceTitleReqDto request);

    /**
     * 删除发票抬头
     *
     * @param request 删除请求
     * @return 操作结果
     */
    @PostMapping("/delete")
    @ApiOperation(value = "删除发票抬头")
    ResponseBase<Void> delete(@Valid @RequestBody DeleteInvoiceTitleReqDto request);

    /**
     * 查询发票抬头列表
     *
     * @param request 列表查询请求
     * @return 发票抬头列表
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询发票抬头列表")
    ResponseBase<PageDTO<InvoiceTitleDTO>> list(@Valid @RequestBody InvoiceTitleListReqDto request);

    /**
     * 查询发票抬头详情
     *
     * @param request 详情查询请求
     * @return 发票抬头详情
     */
    @PostMapping("/detail")
    @ApiOperation(value = "查询发票抬头详情")
    ResponseBase<InvoiceTitleDTO> detail(@Valid @RequestBody InvoiceTitleDetailReqDto request);
}
