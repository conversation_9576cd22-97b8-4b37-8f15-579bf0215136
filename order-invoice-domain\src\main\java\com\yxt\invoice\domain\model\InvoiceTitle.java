package com.yxt.invoice.domain.model;

import cn.hutool.extra.spring.SpringUtil;
import com.yxt.invoice.domain.external.IdService;
import com.yxt.invoice.domain.external.IdService.IdType;
import com.yxt.order.types.invoice.enums.InvoiceBuyerPartyTypeEnum;
import com.yxt.order.types.invoice.enums.InvoiceTypeEnum;
import lombok.Data;

import java.util.Date;

/**
 * 发票抬头领域实体
 * 根据数据库表 invoice_title 设计
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-06
 */
@Data
public class InvoiceTitle {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 抬头内部单号
     */
    private String invoiceTitleNo;

    /**
     * 会员编号
     */
    private String userId;

    /**
     * 发票类型 专票:SPECIAL_INVOICE 普票:ORDINARY_INVOICE
     */
    private InvoiceTypeEnum invoiceType;

    /**
     * 购方类型 个人-INDIVIDUAL 单位-ORGANIZATION
     */
    private InvoiceBuyerPartyTypeEnum buyerPartyType;

    /**
     * 购方名字
     */
    private String buyerName;

    /**
     * 购方个人身份证单位纳税人识别号
     */
    private String buyerTin;

    /**
     * 购方电话
     */
    private String buyerPhone;

    /**
     * 购方地址
     */
    private String buyerAddress;

    /**
     * 购方银行,专票必填
     */
    private String buyerBank;

    /**
     * 购方银行账户,专票必填
     */
    private String buyerBankAccount;

    /**
     * 购方邮箱
     */
    private String buyerEmail;

    /**
     * 购方手机号
     */
    private String buyerMobile;

    /**
     * 是否起效 1-起效 -1-未起效
     */
    private Long isValid;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 系统创建时间
     */
    private Date sysCreateTime;

    /**
     * 系统更新时间
     */
    private Date sysUpdateTime;

    /**
     * 数据版本，每次update+1
     */
    private Long version;

    /**
     * 创建新的发票抬头
     */
    public static InvoiceTitle create(String userId, InvoiceTypeEnum invoiceType, 
                                    InvoiceBuyerPartyTypeEnum buyerPartyType, String buyerName,
                                    String buyerTin, String createdBy) {
        InvoiceTitle invoiceTitle = new InvoiceTitle();
        
        // 生成抬头单号
        String titleNo = SpringUtil.getBean(IdService.class).generateId(IdType.INVOICE_TITLE_NO);
        invoiceTitle.setInvoiceTitleNo(titleNo);
        
        invoiceTitle.setUserId(userId);
        invoiceTitle.setInvoiceType(invoiceType);
        invoiceTitle.setBuyerPartyType(buyerPartyType);
        invoiceTitle.setBuyerName(buyerName);
        invoiceTitle.setBuyerTin(buyerTin);
        invoiceTitle.setCreatedBy(createdBy);
        invoiceTitle.setUpdatedBy(createdBy);
        invoiceTitle.setIsValid(1L); // 默认有效
        
        Date now = new Date();
        invoiceTitle.setSysCreateTime(now);
        invoiceTitle.setSysUpdateTime(now);
        invoiceTitle.setVersion(1L);
        
        return invoiceTitle;
    }

    /**
     * 更新发票抬头信息
     */
    public void update(InvoiceTypeEnum invoiceType, InvoiceBuyerPartyTypeEnum buyerPartyType,
                      String buyerName, String buyerTin, String updatedBy) {
        this.invoiceType = invoiceType;
        this.buyerPartyType = buyerPartyType;
        this.buyerName = buyerName;
        this.buyerTin = buyerTin;
        this.updatedBy = updatedBy;
        this.sysUpdateTime = new Date();
        this.version = this.version + 1;
    }

    /**
     * 设置购方详细信息
     */
    public void setBuyerDetails(String buyerPhone, String buyerAddress, String buyerBank,
                               String buyerBankAccount, String buyerEmail, String buyerMobile) {
        this.buyerPhone = buyerPhone;
        this.buyerAddress = buyerAddress;
        this.buyerBank = buyerBank;
        this.buyerBankAccount = buyerBankAccount;
        this.buyerEmail = buyerEmail;
        this.buyerMobile = buyerMobile;
        this.sysUpdateTime = new Date();
        this.version = this.version + 1;
    }

    /**
     * 删除发票抬头（逻辑删除）
     */
    public void delete(String updatedBy) {
        this.isValid = -1L;
        this.updatedBy = updatedBy;
        this.sysUpdateTime = new Date();
        this.version = this.version + 1;
    }

    /**
     * 验证专票必填字段
     */
    public void validateSpecialInvoiceFields() {
        if (InvoiceTypeEnum.SPECIAL_INVOICE.equals(this.invoiceType)) {
            if (this.buyerBank == null || this.buyerBank.trim().isEmpty()) {
                throw new IllegalArgumentException("专票必须填写购方银行");
            }
            if (this.buyerBankAccount == null || this.buyerBankAccount.trim().isEmpty()) {
                throw new IllegalArgumentException("专票必须填写购方银行账户");
            }
        }
    }

    /**
     * 验证单位类型必填字段
     */
    public void validateOrganizationFields() {
        if (InvoiceBuyerPartyTypeEnum.ORGANIZATION.equals(this.buyerPartyType)) {
            if (this.buyerTin == null || this.buyerTin.trim().isEmpty()) {
                throw new IllegalArgumentException("单位类型必须填写纳税人识别号");
            }
        }
    }

    /**
     * 是否有效
     */
    public boolean isValid() {
        return this.isValid != null && this.isValid == 1L;
    }

    /**
     * 是否为专票
     */
    public boolean isSpecialInvoice() {
        return InvoiceTypeEnum.SPECIAL_INVOICE.equals(this.invoiceType);
    }

    /**
     * 是否为单位类型
     */
    public boolean isOrganization() {
        return InvoiceBuyerPartyTypeEnum.ORGANIZATION.equals(this.buyerPartyType);
    }
}
