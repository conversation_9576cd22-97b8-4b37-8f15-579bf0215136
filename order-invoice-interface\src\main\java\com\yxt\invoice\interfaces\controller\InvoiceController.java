package com.yxt.invoice.interfaces.controller;

import com.yxt.invoice.domain.command.ApplyInvoiceCommand;
import com.yxt.invoice.domain.command.OrderExistsInvoiceQueryCommand;
import com.yxt.invoice.domain.command.ExistsThirdOrderInvoiceCommand;
import com.yxt.invoice.domain.command.RedCreditInvoiceCommand;
import com.yxt.invoice.domain.model.valueobject.ExistsOrderInvoice;
import com.yxt.invoice.interfaces.converter.InvoiceDTOConverter;
import com.yxt.invoice.interfaces.converter.InvoiceRequestConverter;
import com.yxt.invoice.interfaces.service.InvoiceService;
import com.yxt.invoice.sdk.api.InvoiceApi;
import com.yxt.invoice.sdk.api.InvoiceQueryApi;
import com.yxt.invoice.sdk.dto.InvoiceMainDTO;
import com.yxt.invoice.sdk.dto.req.ApplyInvoiceMainReqDto;
import com.yxt.invoice.sdk.dto.req.ApplyRedInvoiceMainReqDto;
import com.yxt.invoice.sdk.dto.req.QueryOrderExistsInvoiceReqDto;
import com.yxt.invoice.sdk.dto.req.QueryThirdOrderExistsInvoiceReqDto;
import com.yxt.invoice.sdk.dto.req.UserInvoiceListReq;
import com.yxt.invoice.sdk.dto.res.QueryOrderExistsInvoiceResDto;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import io.swagger.annotations.ApiOperation;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 发票控制器 实现SDK接口，提供RESTful API
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@ApiOperation(value = "Invoice核心接口")
@RestController
@RequestMapping("/api/1.0/invoice")
@Slf4j
public class InvoiceController implements InvoiceApi, InvoiceQueryApi {

  @Resource
  private InvoiceService invoiceService;


  @Override
  public ResponseBase<String> apply(ApplyInvoiceMainReqDto reqDto) {
    // 转换为Command
    ApplyInvoiceCommand command = InvoiceRequestConverter.createInvoiceCommand(reqDto);

    return ResponseBase.success(invoiceService.applyInvoice(command));
  }


  @Override
  public ResponseBase<String> applyRed(ApplyRedInvoiceMainReqDto reqDto) {
    RedCreditInvoiceCommand command = InvoiceRequestConverter.createRedInvoiceCommand(reqDto);
    return ResponseBase.success(invoiceService.applyRedInvoice(command));
  }

  @Override
  public ResponseBase<QueryOrderExistsInvoiceResDto> queryOrderExistsInvoice(
      QueryOrderExistsInvoiceReqDto req) {
    OrderExistsInvoiceQueryCommand command = InvoiceRequestConverter.convertToExistsOrderInvoiceCommand(
        req);
    ExistsOrderInvoice existsInvoice = invoiceService.queryOrderExistsInvoice(command);

    QueryOrderExistsInvoiceResDto resDto = InvoiceDTOConverter.convertToExistsOrderInvoice(
        existsInvoice);
    return ResponseBase.success(resDto);
  }

  @Override
  public ResponseBase<QueryOrderExistsInvoiceResDto> queryThirdOrderExistsInvoiceReqDto(
      QueryThirdOrderExistsInvoiceReqDto req) {
    ExistsThirdOrderInvoiceCommand command = InvoiceRequestConverter.convertToExistsThirdOrderInvoiceCommand(
        req);
    ExistsOrderInvoice existsInvoice = invoiceService.queryThirdOrderExistsInvoiceReqDto(command);

    QueryOrderExistsInvoiceResDto resDto = InvoiceDTOConverter.convertToExistsOrderInvoice(
        existsInvoice);
    return ResponseBase.success(resDto);
  }


  @Override
  public ResponseBase<PageDTO<InvoiceMainDTO>> userInvoiceList(UserInvoiceListReq req) {
    return ResponseBase.success(invoiceService.userInvoiceList(req));
  }
}
