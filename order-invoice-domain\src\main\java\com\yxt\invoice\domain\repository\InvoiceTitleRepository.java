package com.yxt.invoice.domain.repository;

import com.yxt.invoice.domain.model.InvoiceTitle;

import java.util.List;

/**
 * 发票抬头仓储接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-06
 */
public interface InvoiceTitleRepository {

    /**
     * 保存发票抬头
     *
     * @param invoiceTitle 发票抬头
     * @return 保存后的发票抬头
     */
    InvoiceTitle save(InvoiceTitle invoiceTitle);

    /**
     * 根据抬头单号查询
     *
     * @param invoiceTitleNo 抬头单号
     * @return 发票抬头
     */
    InvoiceTitle findByInvoiceTitleNo(String invoiceTitleNo);

    /**
     * 根据用户ID查询有效的发票抬头列表
     *
     * @param userId 用户ID
     * @return 发票抬头列表
     */
    List<InvoiceTitle> findValidByUserId(String userId);

    /**
     * 根据抬头单号删除（逻辑删除）
     *
     * @param invoiceTitleNo 抬头单号
     * @param updatedBy      更新人
     * @return 是否删除成功
     */
    boolean deleteByInvoiceTitleNo(String invoiceTitleNo, String updatedBy);

    /**
     * 更新发票抬头
     *
     * @param invoiceTitle 发票抬头
     * @return 更新后的发票抬头
     */
    InvoiceTitle update(InvoiceTitle invoiceTitle);
}
