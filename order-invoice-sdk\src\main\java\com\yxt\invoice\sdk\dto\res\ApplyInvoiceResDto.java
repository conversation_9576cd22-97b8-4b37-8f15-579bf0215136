package com.yxt.invoice.sdk.dto.res;

import com.yxt.invoice.sdk.dto.InvoiceDetailDTO;
import com.yxt.invoice.sdk.dto.InvoiceMainDTO;
import com.yxt.lang.dto.api.ResponseDTO;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 发票申请响应
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ApplyInvoiceResDto extends ResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 发票信息
     */
    @ApiModelProperty(value = "发票主信息")
    private InvoiceMainDTO invoiceMain;

    /**
     * 发票明细列表
     */
    @ApiModelProperty(value = "发票明细列表")
    private List<InvoiceDetailDTO> details;

}
