package com.yxt.invoice.application.service.impl;

import com.google.common.base.Preconditions;
import com.yxt.invoice.application.service.InvoiceApplicationService;
import com.yxt.invoice.application.service.impl.builder.AbstractApplyInvoiceCommandBuilder;
import com.yxt.invoice.application.service.impl.exists_invoice_query.AbstractExistsInvoiceQuery;
import com.yxt.invoice.application.service.impl.exists_invoice_query_third.AbstractExistsInvoiceQueryThird;
import com.yxt.invoice.domain.command.ApplyInvoiceCommand;
import com.yxt.invoice.domain.command.ExistsThirdOrderInvoiceCommand;
import com.yxt.invoice.domain.command.OrderExistsInvoiceQueryCommand;
import com.yxt.invoice.domain.command.QueryInvoiceDetailCommand;
import com.yxt.invoice.domain.command.QueryInvoiceListCommand;
import com.yxt.invoice.domain.command.RedCreditInvoiceCommand;
import com.yxt.invoice.domain.command.user.UserQueryInvoiceListCommand;
import com.yxt.invoice.domain.factory.InvoiceAggregateFactory;
import com.yxt.invoice.domain.model.InvoiceMain;
import com.yxt.invoice.domain.model.aggregate.InvoiceAggregate;
import com.yxt.invoice.domain.model.valueobject.ExistsOrderInvoice;
import com.yxt.invoice.domain.repository.InvoiceRepository;
import com.yxt.invoice.domain.repository.ProviderInvoiceRepository;
import com.yxt.invoice.domain.repository.RemoteDataResult;
import com.yxt.invoice.infrastructure.common.utils.MessageBuildUtils;
import com.yxt.invoice.infrastructure.message.InvoiceDomainEventProducer;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.order.types.invoice.enums.InvoiceBusinessTypeEnum;
import com.yxt.order.types.invoice.enums.InvoiceDataSourceEnum;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * 发票应用服务实现
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@Slf4j
@Service
public class InvoiceApplicationServiceImpl implements InvoiceApplicationService {

  @Resource
  private List<AbstractApplyInvoiceCommandBuilder> abstractApplyInvoiceCommandBuilderList;

  @Resource
  private List<AbstractExistsInvoiceQuery> abstractExistsInvoiceQueryList;


  @Resource
  private List<AbstractExistsInvoiceQueryThird> abstractExistsInvoiceQueryThirdList;

  @Resource
  private InvoiceRepository invoiceRepository;

  @Resource
  private ProviderInvoiceRepository providerInvoiceRepository;

  @Resource
  private InvoiceDomainEventProducer invoiceDomainEventProducer;

  @Resource
  private InvoiceAggregateFactory invoiceAggregateFactory;

  @Override
  public String applyInvoice(ApplyInvoiceCommand command) {
    String orderNo = command.getInvoiceMain().getOrderNo().getOrderNo();
    List<InvoiceAggregate> invoiceAggregateList = invoiceRepository.findInvoiceByOrderNo(orderNo);
    if (invoiceAggregateList != null) {
      String detail = MessageBuildUtils.buildInvoiceStatus(invoiceAggregateList);
      throw new RuntimeException(
          String.format("%s 订单已有开票记录,请勿触发申请开票接口. 详细信息:%s", orderNo, detail));
    }

    // 组装Command
    for (AbstractApplyInvoiceCommandBuilder builder : abstractApplyInvoiceCommandBuilderList) {
      if (builder.route(command)) {
        command = builder.build(command);
        break;
      }
    }

    // 命令转模型
    InvoiceAggregate aggravate = invoiceAggregateFactory.createAggravate(command);

    OrderExistsInvoiceQueryCommand existsInvoiceCommand = aggravate.createExistsInvoiceCommand();
    // 检查DB
    Boolean dbExists = invoiceRepository.checkExistsFromDb(existsInvoiceCommand);
    Preconditions.checkArgument(!dbExists,
        String.format("%s 订单票据在数据库已存在,请勿重新开票", orderNo));
    // 检查远程
    RemoteDataResult remoteDataResult = invoiceRepository.checkExistsFromRemote(
        existsInvoiceCommand);
    if (remoteDataResult.getResult()) {
      aggravate.rePullEvent(remoteDataResult.getRemoteDetailList()); // 添加重新拉取事件
      invoiceDomainEventProducer.sendDomainEvents(aggravate.getDomainEvents());
      throw new RuntimeException(
          String.format("%s 订单票据在POS已经开过票,请勿重新开票。心云系统已触发票据拉取中",
              orderNo));
    }

    // 请求三方
    providerInvoiceRepository.applyInvoice(aggravate);

    // 入库
    invoiceRepository.doSave(aggravate);

    // 返回发票单号
    return aggravate.getInvoiceMain().getInvoiceMainNo();

  }


  @Override
  public String applyRedInvoice(RedCreditInvoiceCommand command) {
    String invoiceMainNo = command.getInvoiceMainNo();
    InvoiceAggregate aggregate = invoiceRepository.findByInvoiceMainNo(invoiceMainNo);
    Preconditions.checkArgument(Objects.nonNull(aggregate), "蓝票不存在");
    Preconditions.checkArgument(aggregate.isAllowRedInvoice(), "蓝票未开成功,不允许红冲");
    Preconditions.checkArgument(
        InvoiceDataSourceEnum.INVOICE.name().equals(aggregate.getInvoiceMain().getDataSource()),
        "POS系統开票成功的,在心云系统不允许红冲,请在POS系统红冲");

    // 数据库校验是否已经存在
    InvoiceAggregate redInvoice = invoiceRepository.findByRedInvoiceMainNo(invoiceMainNo);
    Preconditions.checkArgument(Objects.isNull(redInvoice), "红票已经开过,请勿重新开票");

    // 创建红票聚合根
    InvoiceAggregate redAggregate = invoiceAggregateFactory.redAggregate(aggregate, command);

    Preconditions.checkArgument(!aggregate.isRedInvoice() && redAggregate.isRedInvoice(),
        "冲红遇到非法数据,请检查");

    // 调用红冲接口
    providerInvoiceRepository.applyRedInvoice(redAggregate);

    // 持久化 保存更新
    invoiceRepository.doSaveRedInvoice(redAggregate);

    return redAggregate.getInvoiceMain().getInvoiceMainNo();
  }


  @Override
  public ExistsOrderInvoice queryOrderExistsInvoice(OrderExistsInvoiceQueryCommand command) {
    String orderNo = command.getOrderNo();
    InvoiceBusinessTypeEnum businessType = command.getBusinessType();
    List<InvoiceAggregate> invoiceAggregateList = invoiceRepository.findInvoiceByOrderNo(orderNo);
    if (!CollectionUtils.isEmpty(invoiceAggregateList)) {
      return ExistsOrderInvoice.getHasExistsOrderInvoice(invoiceAggregateList);
    }

    for (AbstractExistsInvoiceQuery abstractExistsInvoiceQuery : abstractExistsInvoiceQueryList) {
      if (abstractExistsInvoiceQuery.route(businessType)) {
        return abstractExistsInvoiceQuery.build(command);
      }
    }

    throw new RuntimeException(String.format("%s 暂不支持", businessType));
  }

  @Override
  public ExistsOrderInvoice queryThirdOrderExistsInvoiceReqDto(
      ExistsThirdOrderInvoiceCommand command) {

    OrderExistsInvoiceQueryCommand queryCommand = null;
    for (AbstractExistsInvoiceQueryThird abstractExistsInvoiceQueryThird : abstractExistsInvoiceQueryThirdList) {
      queryCommand = abstractExistsInvoiceQueryThird.build(command.getThirdOrderNo());
      if (Objects.nonNull(queryCommand)) {
        break;
      }
    }

    Preconditions.checkArgument(queryCommand != null, "此订单非一心堂订单,请核对信息");
    return queryOrderExistsInvoice(queryCommand);
  }

  @Override
  @Transactional(readOnly = true)
  public PageDTO<InvoiceMain> pageInvoiceList(QueryInvoiceListCommand query) {
    return invoiceRepository.pageInvoiceList(query);
  }

  @Override
  @Transactional(readOnly = true)
  public InvoiceAggregate queryInvoiceDetail(QueryInvoiceDetailCommand query) {
    // 查询发票聚合根
    InvoiceAggregate aggregate = invoiceRepository.findByInvoiceMainNo(query.getInvoiceMainNo());
    Preconditions.checkArgument(aggregate != null, "开票单号不存在");
    return aggregate;
  }

  @Override
  public PageDTO<InvoiceMain> userInvoiceList(UserQueryInvoiceListCommand command) {
    return invoiceRepository.userInvoiceList(command);
  }
}
