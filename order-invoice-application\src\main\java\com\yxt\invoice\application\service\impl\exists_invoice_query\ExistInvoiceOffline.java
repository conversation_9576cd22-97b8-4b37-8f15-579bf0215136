package com.yxt.invoice.application.service.impl.exists_invoice_query;

import com.google.common.base.Preconditions;
import com.yxt.invoice.application.service.impl.builder.OfflineCommandBuild;
import com.yxt.invoice.application.third.order.feign.OfflineOrderQueryApiFeign;
import com.yxt.invoice.domain.command.OrderExistsInvoiceQueryCommand;
import com.yxt.invoice.domain.model.valueobject.ExistsOrderInvoice;
import com.yxt.invoice.domain.model.valueobject.InvoiceAmount;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.open.message.offline_order.model.CommonOfflineRefundOrderModel;
import com.yxt.order.open.message.offline_order.model.OfflineOrderModel;
import com.yxt.order.open.message.offline_order.model.OfflineRefundOrderModel;
import com.yxt.order.open.sdk.offline_order.req.CommonOfflineRefundOrderQueryReqDto;
import com.yxt.order.open.sdk.offline_order.req.OfflineOrderDetailQueryReqDto;
import com.yxt.order.open.sdk.offline_order.req.OfflineRefundOrderDetailQueryReqDto;
import com.yxt.order.types.invoice.enums.InvoiceBusinessTypeEnum;
import com.yxt.order.types.invoice.enums.InvoiceTransactionChannelEnum;
import com.yxt.order.types.offline.enums.RefundTypeEnum;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
public class ExistInvoiceOffline extends AbstractExistsInvoiceQuery {

  @Resource
  private OfflineCommandBuild offlineCommandBuild;

  @Resource
  private OfflineOrderQueryApiFeign offlineOrderQueryApiFeign;

  @Override
  public Boolean route(InvoiceBusinessTypeEnum businessTypeEnum) {
    return InvoiceBusinessTypeEnum.OFFLINE.equals(businessTypeEnum);
  }


  @Override
  public ExistsOrderInvoice build(OrderExistsInvoiceQueryCommand command) {
    String orderNo = command.getOrderNo();

    ExistsOrderInvoice existsOrderInvoice = new ExistsOrderInvoice();

    OfflineOrderDetailQueryReqDto queryReqDto = new OfflineOrderDetailQueryReqDto();
    queryReqDto.setOrderNo(orderNo);
    //查询线下单补充
    ResponseBase<OfflineOrderModel> offlineOrderModelResponseBase = offlineOrderQueryApiFeign.detail(
        queryReqDto);
    Preconditions.checkArgument(offlineOrderModelResponseBase.checkSuccess(),
        "查询线下单数据异常,暂不支持开票");
    OfflineOrderModel data = offlineOrderModelResponseBase.getData();

    CommonOfflineRefundOrderQueryReqDto reqDto = new CommonOfflineRefundOrderQueryReqDto();
    reqDto.setOrderNo(orderNo);

    ResponseBase<CommonOfflineRefundOrderModel> refundRes = offlineOrderQueryApiFeign.commonRefundInfo(
        reqDto);
    if (!refundRes.getCode().equals("60162")) {
      Preconditions.checkArgument(refundRes.checkSuccess(),
          "查询线下单退单数据异常,暂不支持开票");
    }

    List<OfflineRefundOrderModel> refundDataList = new ArrayList<>();

    if (Objects.nonNull(refundRes.getData()) && !CollectionUtils.isEmpty(
        refundRes.getData().getRefundOrderModelList())) {

      for (OfflineRefundOrderModel iOfflineRefundOrderRes : refundRes.getData()
          .getRefundOrderModelList()) {
        OfflineRefundOrderDetailQueryReqDto refundDetailQueryReqDto = new OfflineRefundOrderDetailQueryReqDto();
        refundDetailQueryReqDto.setRefundNo(
            iOfflineRefundOrderRes.getBaseRefundInfo().getRefundNo().getRefundNo());
        ResponseBase<OfflineRefundOrderModel> offlineRefundOrderModelResponseBase = offlineOrderQueryApiFeign.refundDetail(
            refundDetailQueryReqDto);
        Preconditions.checkArgument(offlineRefundOrderModelResponseBase.checkSuccess(),
            "查询线下单退单数据异常,暂不支持开票");
        Preconditions.checkArgument(
            !offlineRefundOrderModelResponseBase.getData().getBaseRefundInfo()
                .getRefundTypeValue().equals(RefundTypeEnum.ALL.name()), "暂不支持整单退开票");
        refundDataList.add(offlineRefundOrderModelResponseBase.getData());
      }
    }

    List<InvoiceAmount> invoiceAmountList = offlineCommandBuild.convertByOfflineOrder(data,
        refundDataList);
    existsOrderInvoice.setInvoiceAmounts(invoiceAmountList);
    existsOrderInvoice.setThirdPlatformCode(data.getBaseOrderInfo().getThirdPlatformCodeValue());
    existsOrderInvoice.setThirdOrderNo(
        data.getBaseOrderInfo().getThirdOrderNo().getThirdOrderNo());
    existsOrderInvoice.setOrderNo(data.getBaseOrderInfo().getOrderNo().getOrderNo());
    existsOrderInvoice.setPosNo(data.getBaseOrderInfo().getThirdOrderNo().getThirdOrderNo());
    existsOrderInvoice.setTransactionChannel(InvoiceTransactionChannelEnum.OFFLINE);
    existsOrderInvoice.setBusinessType(InvoiceBusinessTypeEnum.OFFLINE);
    return existsOrderInvoice;
  }

}
