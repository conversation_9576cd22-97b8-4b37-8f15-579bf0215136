package com.yxt.invoice.interfaces.converter;

import com.yxt.invoice.domain.model.InvoiceDetail;
import com.yxt.invoice.domain.model.InvoiceMain;
import com.yxt.invoice.domain.model.aggregate.InvoiceAggregate;
import com.yxt.invoice.domain.model.valueobject.ExistsOrderInvoice;
import com.yxt.invoice.domain.model.valueobject.InvoiceAmount;
import com.yxt.invoice.sdk.dto.InvoiceAmountDTO;
import com.yxt.invoice.sdk.dto.InvoiceDetailDTO;
import com.yxt.invoice.sdk.dto.InvoiceMainDTO;
import com.yxt.invoice.sdk.dto.res.InvoiceDetailResponse;
import com.yxt.invoice.sdk.dto.res.QueryOrderExistsInvoiceResDto;
import java.util.Objects;
import java.util.stream.Collectors;
import org.springframework.util.CollectionUtils;

/**
 * 发票DTO转换器 负责Domain对象与SDK DTO之间的转换
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
public class InvoiceDTOConverter {


  /**
   * Domain对象转换为SDK DTO - InvoiceMainDTO
   */
  public static InvoiceMainDTO convertToInvoiceMainDTO(InvoiceMain invoiceMain) {
    if (invoiceMain == null) {
      return null;
    }

    InvoiceMainDTO dto = new InvoiceMainDTO();

    // 基本字段转换
    dto.setId(invoiceMain.getId());
    dto.setCompanyCode(invoiceMain.getCompanyCode());
    dto.setCompanyName(invoiceMain.getCompanyName());
    dto.setOrganizationCode(invoiceMain.getOrganizationCode());
    dto.setOrganizationName(invoiceMain.getOrganizationName());
    dto.setInvoiceMainNo(invoiceMain.getInvoiceMainNo());
    dto.setThirdPlatformCode(invoiceMain.getThirdPlatformCode());
    dto.setThirdOrderNo(invoiceMain.getThirdOrderNo().getThirdOrderNo());
    dto.setOrderNo(invoiceMain.getOrderNo().getOrderNo());
    dto.setPosNo(invoiceMain.getPosNo());
    dto.setUserId(invoiceMain.getUserId());
    dto.setMerCode(invoiceMain.getMerCode());
    dto.setTransactionChannel(invoiceMain.getTransactionChannel());
    dto.setRemoteInvoiceCode(invoiceMain.getRemoteInvoiceCode());
    dto.setRemoteInvoiceResponseId(invoiceMain.getRemoteInvoiceResponseId());
    if (Objects.nonNull(invoiceMain.getRemoteWriteOffState())) {
      dto.setRemoteWriteOffState(invoiceMain.getRemoteWriteOffState().name());
    }
    dto.setInvoiceRedBlueType(invoiceMain.getInvoiceRedBlueType());
    dto.setRedInvoiceMainNo(invoiceMain.getRedInvoiceMainNo());
    dto.setRedInvoiceReason(invoiceMain.getRedInvoiceReason());
    dto.setNotes(invoiceMain.getNotes());

    // 枚举类型直接赋值
    dto.setInvoiceType(invoiceMain.getInvoiceType());
    dto.setInvoiceStatus(invoiceMain.getInvoiceStatus());
    dto.setSyncStatus(invoiceMain.getSyncStatus());
    dto.setBuyerPartyType(invoiceMain.getBuyerPartyType());

    // 金额字段
    dto.setActualPayAmount(invoiceMain.getActualPayAmount());
    dto.setDeliveryAmount(invoiceMain.getDeliveryAmount());
    dto.setInvoiceAmount(invoiceMain.getInvoiceAmount());
    dto.setTaxAmount(invoiceMain.getTaxAmount());
    dto.setPriceTaxAmount(invoiceMain.getPriceTaxAmount());

    // 其他字段
    dto.setDeliveryType(invoiceMain.getDeliveryType());
    dto.setSplitBill(invoiceMain.getSplitBill());
    dto.setOrderCreated(invoiceMain.getOrderCreated());
    dto.setPdfUrl(invoiceMain.getPdfUrl());
    dto.setInvoiceErrMsg(invoiceMain.getInvoiceErrMsg());
    dto.setApplyTime(invoiceMain.getApplyTime());
    dto.setOperator(invoiceMain.getOperator());
    dto.setPayee(invoiceMain.getPayee());
    dto.setReviewed(invoiceMain.getReviewed());
    dto.setApplyChannel(invoiceMain.getApplyChannel());

    // 销方信息
    dto.setSellerNumber(invoiceMain.getSellerNumber());
    dto.setSellerName(invoiceMain.getSellerName());
    dto.setSellerTin(invoiceMain.getSellerTin());
    dto.setSellerAddress(invoiceMain.getSellerAddress());
    dto.setSellerPhone(invoiceMain.getSellerPhone());
    dto.setSellerBank(invoiceMain.getSellerBank());
    dto.setSellerBankAccount(invoiceMain.getSellerBankAccount());

    // 购方信息
    dto.setBuyerName(invoiceMain.getBuyerName());
    dto.setBuyerTin(invoiceMain.getBuyerTin());
    dto.setBuyerAddress(invoiceMain.getBuyerAddress());
    dto.setBuyerPhone(invoiceMain.getBuyerPhone());
    dto.setBuyerBank(invoiceMain.getBuyerBank());
    dto.setBuyerBankAccount(invoiceMain.getBuyerBankAccount());
    dto.setBuyerEmail(invoiceMain.getBuyerEmail());
    dto.setBuyerMobile(invoiceMain.getBuyerMobile());
    dto.setShowBuyerBankAccount(invoiceMain.getShowBuyerBankAccount());

    // 系统字段
    dto.setIsValid(invoiceMain.getIsValid());
    dto.setCreated(invoiceMain.getCreated());
    dto.setUpdated(invoiceMain.getUpdated());
    dto.setCreatedBy(invoiceMain.getCreatedBy());
    dto.setUpdatedBy(invoiceMain.getUpdatedBy());
    dto.setSysCreateTime(invoiceMain.getSysCreateTime());
    dto.setSysUpdateTime(invoiceMain.getSysUpdateTime());
    dto.setVersion(invoiceMain.getVersion());
    dto.setDataSource(invoiceMain.getDataSource());

    return dto;
  }


  /**
   * Domain对象转换为SDK DTO - InvoiceDetailDTO
   */
  public static InvoiceDetailDTO convertToInvoiceDetailDTO(InvoiceDetail invoiceDetail) {
    if (invoiceDetail == null) {
      return null;
    }

    InvoiceDetailDTO dto = new InvoiceDetailDTO();

    dto.setId(invoiceDetail.getId());
    dto.setInvoiceMainNo(invoiceDetail.getInvoiceMainNo());
    dto.setInvoiceDetailNo(invoiceDetail.getInvoiceDetailNo());
    dto.setRowNo(invoiceDetail.getRowNo());
    dto.setLine(invoiceDetail.getLine());
    dto.setTaxClassificationCode(invoiceDetail.getTaxClassificationCode());
    dto.setTopLevelTaxClassificationCode(invoiceDetail.getTopLevelTaxClassificationCode());
    dto.setErpCode(invoiceDetail.getErpCode());
    dto.setErpName(invoiceDetail.getErpName());
    dto.setCommodityCount(invoiceDetail.getCommodityCount());
    dto.setCommoditySpec(invoiceDetail.getCommoditySpec());
    dto.setUnit(invoiceDetail.getUnit());
    dto.setPrice(invoiceDetail.getPrice());
    dto.setTotalAmount(invoiceDetail.getTotalAmount());
    dto.setTaxAmount(invoiceDetail.getTaxAmount());
    dto.setTaxRate(invoiceDetail.getTaxRate());
    dto.setTaxRateCode(invoiceDetail.getTaxRateCode());
    dto.setPriceTaxAmount(invoiceDetail.getPriceTaxAmount());
    dto.setInvoiceLineType(invoiceDetail.getInvoiceLineType());
    dto.setDiscountAmount(invoiceDetail.getDiscountAmount());
    dto.setPolicyStatus(invoiceDetail.getPolicyStatus());
    dto.setPolicyTag(invoiceDetail.getPolicyTag());
    dto.setPolicyTaxRate(invoiceDetail.getPolicyTaxRate());
    dto.setIsValid(invoiceDetail.getIsValid());
    dto.setCreated(invoiceDetail.getCreated());
    dto.setUpdated(invoiceDetail.getUpdated());
    dto.setCreatedBy(invoiceDetail.getCreatedBy());
    dto.setUpdatedBy(invoiceDetail.getUpdatedBy());
    dto.setSysCreateTime(invoiceDetail.getSysCreateTime());
    dto.setSysUpdateTime(invoiceDetail.getSysUpdateTime());
    dto.setVersion(invoiceDetail.getVersion());

    return dto;
  }


  public static InvoiceDetailResponse convertToInvoiceDetailResponse(InvoiceAggregate aggregate) {

    return new InvoiceDetailResponse(
        InvoiceDTOConverter.convertToInvoiceMainDTO(aggregate.getInvoiceMain()),
        aggregate.getInvoiceDetailList().stream()
            .map(InvoiceDTOConverter::convertToInvoiceDetailDTO).collect(Collectors.toList()));
  }

  public static QueryOrderExistsInvoiceResDto convertToExistsOrderInvoice(ExistsOrderInvoice info) {

    QueryOrderExistsInvoiceResDto resDto = new QueryOrderExistsInvoiceResDto();
    resDto.setThirdPlatformCode(info.getThirdPlatformCode());
    resDto.setThirdOrderNo(info.getThirdOrderNo());
    resDto.setOrderNo(info.getOrderNo());
    resDto.setPosNo(info.getPosNo());
    resDto.setTransactionChannel(info.getTransactionChannel());
    resDto.setBusinessType(info.getBusinessType());
    if (!CollectionUtils.isEmpty(info.getInvoiceMains())) {
      resDto.setInvoiceMains(
          info.getInvoiceMains().stream().map(InvoiceDTOConverter::convertToInvoiceMainDTO)
              .collect(Collectors.toList()));
    }
    if (!CollectionUtils.isEmpty(info.getInvoiceAmounts())) {
      resDto.setInvoiceAmounts(
          info.getInvoiceAmounts().stream().map(InvoiceDTOConverter::convertToInvoiceAmountDTO)
              .collect(Collectors.toList()));
    }
    return resDto;

  }

  private static InvoiceAmountDTO convertToInvoiceAmountDTO(InvoiceAmount invoiceAmount) {

    InvoiceAmountDTO invoiceAmountDTO = new InvoiceAmountDTO();
    invoiceAmountDTO.setKey(invoiceAmount.getKey());
    invoiceAmountDTO.setAmount(invoiceAmount.getAmount());
    return invoiceAmountDTO;
  }
}
