package com.yxt.invoice.sdk.api;

import com.yxt.invoice.sdk.dto.req.SyncInvoiceFromRemoteReq;
import com.yxt.invoice.sdk.dto.req.UpdateInvoiceFromRemoteReq;
import com.yxt.lang.dto.api.ResponseBase;
import io.swagger.annotations.ApiOperation;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface InvoiceOpsApi {


  @PostMapping("/updateInvoiceFromRemote")
  @ApiOperation(value = "从税务云系统获取详情,来更新发票系统发票")
  ResponseBase<String> updateInvoiceFromRemote(@Valid @RequestBody UpdateInvoiceFromRemoteReq req);


  @PostMapping("/syncInvoiceFromRemote")
  @ApiOperation(value = "POS系统已经开票,但是发票系统没有,从税务云系统获取详情,来写入发票系统")
  ResponseBase<String> syncInvoiceFromRemote(@Valid @RequestBody SyncInvoiceFromRemoteReq req);

}
