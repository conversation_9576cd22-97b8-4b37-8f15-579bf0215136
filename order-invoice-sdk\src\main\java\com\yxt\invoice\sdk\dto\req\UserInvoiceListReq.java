package com.yxt.invoice.sdk.dto.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yxt.invoice.sdk.dto.req.enums.QueryInvoiceStatusEnum;
import com.yxt.lang.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import javax.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户开票记录请求
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UserInvoiceListReq extends PageBase implements Serializable {

  @NotEmpty
  @ApiModelProperty("用户Id")
  private String userId;

  /**
   * 查询的开票状态
   */
  @ApiModelProperty("查询开票状态 INVOICING-开票中 INVOICED-已开票")
  private QueryInvoiceStatusEnum queryInvoiceStatus;

  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  @ApiModelProperty(value = "申请开票开始时间", example = "2025-08-01 00:00:00")
  private Date applyStartDate;

  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  @ApiModelProperty(value = "申请开票结束时间", example = "2025-08-11 23:59:59")
  private Date applyEndDate;

}
