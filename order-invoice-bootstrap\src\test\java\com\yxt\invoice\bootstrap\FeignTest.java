package com.yxt.invoice.bootstrap;
import com.yxt.invoice.infrastructure.provider.dto.req.red_invoice.RedInvoiceRequestData;

import com.google.common.collect.Lists;
import com.yxt.invoice.infrastructure.provider.dto.req.invoice.InvoiceRequest;
import com.yxt.invoice.infrastructure.provider.dto.req.invoice.InvoiceRequestData;
import com.yxt.invoice.infrastructure.provider.dto.req.invoice.XinYunOrder;
import com.yxt.invoice.infrastructure.provider.dto.req.red_invoice.RedInvoiceRequest;
import com.yxt.lang.util.JsonUtils;
import org.junit.Test;

public class FeignTest {

  @Test
  public void testOrder() {
    InvoiceRequest invoiceRequest = new InvoiceRequest();

    InvoiceRequestData data = new InvoiceRequestData();
    data.setOutRequestCode("");
    data.setInTaxRateTag("");
    data.setInvoiceType("");
    data.setBuyerType("");
    data.setBuyerName("");
    data.setBuyerTin("");
    data.setBuyerMobile("");
    data.setBuyerAddress("");
    data.setBuyerPhone("");
    data.setSellerNumber("");
    data.setShowBuyerBankAccount("");
    data.setBuyerBank("");
    data.setBuyerBankAccount("");
    data.setPayee("");
    data.setNotes("");
//    data.setSplitItem("");
    data.setRequestDate("");
    data.setOperator("");
    data.setReviewedBy("");
    data.setSpecificElements("");
    data.setItemList(Lists.newArrayList());

    XinYunOrder xinYunOrder = new XinYunOrder();
    xinYunOrder.setThirdOrderNo("三方单号");

    data.setXinYunOrder(xinYunOrder);

    invoiceRequest.setData(Lists.newArrayList(data));
    invoiceRequest.setPId("");
    invoiceRequest.setPSecret("");

    System.out.println(JsonUtils.toJson(invoiceRequest));

  }

  @Test
  public void testRefundOrder() {
    RedInvoiceRequest red = new RedInvoiceRequest();
    RedInvoiceRequestData data = new RedInvoiceRequestData();
    data.setSellerNumber("");
    data.setOutRequestCode("");
    data.setOriginalOutRequestCode("");
    data.setWriteOffReason("");
    data.setIsEntire("");

    red.setData(data);
    red.setPId("");
    red.setPSecret("");

    System.out.println(JsonUtils.toJson(red));
  }
}
