package com.yxt.invoice.sdk.dto.req;

import com.yxt.invoice.sdk.dto.InvoiceAmountDTO;
import com.yxt.order.types.invoice.enums.InvoiceApplyChannelEnum;
import com.yxt.order.types.invoice.enums.InvoiceBusinessTypeEnum;
import com.yxt.order.types.invoice.enums.InvoiceBuyerPartyTypeEnum;
import com.yxt.order.types.invoice.enums.InvoiceShowBuyerBankAccountEnum;
import com.yxt.order.types.invoice.enums.InvoiceTransactionChannelEnum;
import com.yxt.order.types.invoice.enums.InvoiceTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;
import javax.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ApplyInvoiceMainReqDto implements Serializable {

  private static final long serialVersionUID = 1L;

  /**
   * 商户编码
   */
  @ApiModelProperty(value = "商户编码", required = true, example = "500001")
  private String merCode;


  /**
   * 订单号
   */
  @ApiModelProperty(value = "订单号", required = true, example = "1841497488788599302")
  private String orderNo;
  /**
   * 业务类型  O2O、B2C、B2B、OFFLINE
   */
  @ApiModelProperty("业务类型  O2O、B2C、B2B、OFFLINE")
  private InvoiceBusinessTypeEnum businessType;


  /**
   * 交易场景 ONLINE:线上交易, OFFLINE:线下交易
   */
  @ApiModelProperty(value = "交易场景 ONLINE:线上交易, OFFLINE:线下交易", required = true, allowableValues = "ONLINE,OFFLINE")
  private InvoiceTransactionChannelEnum transactionChannel;


  /**
   * 操作人
   */
  @ApiModelProperty(value = "操作人ID", required = true, example = "USER123456")
  @NotEmpty(message = "操作人ID不能为空")
  private String operatorUserId;


  /**
   * 会员编号
   */
  @ApiModelProperty("会员编号,必填")
  private String userId;

  /**
   * 发票类型 蓝票必填
   */
  @ApiModelProperty("发票类型,专票:SPECIAL_INVOICE 普票:ORDINARY_INVOICE ,必填")
  private InvoiceTypeEnum invoiceType;


  /**
   * 备注
   */
  @ApiModelProperty("备注,可选")
  private String notes;


  /**
   * 申请开票时间
   */
  @ApiModelProperty("申请开票时间,必填")
  private Date applyTime;


  /**
   * 申请渠道 一心到家-YXDJ 心云-XY 海典H2-H2POS
   */
  @ApiModelProperty("申请渠道 一心到家:YXDJ 心云:XY 海典H2:H2POS,必填")
  private InvoiceApplyChannelEnum applyChannel;


  /**
   * 购方类型
   */
  @ApiModelProperty("购方类型 个人:INDIVIDUAL  单位:ORGANIZATION,必填")
  private InvoiceBuyerPartyTypeEnum buyerPartyType;

  /**
   * 购方名称
   */
  @ApiModelProperty("购方名称,必填")
  private String buyerName;

  /**
   * 购方税号（个人身份证/单位纳税人识别号）
   */
  @ApiModelProperty("购方税号（个人身份证/单位纳税人识别号）,必填")
  private String buyerTin;

  /**
   * 购方地址
   */
  @ApiModelProperty("购方地址,可选")
  private String buyerAddress;

  /**
   * 购方电话
   */
  @ApiModelProperty("购方电话,可选")
  private String buyerPhone;

  /**
   * 购方银行
   */
  @ApiModelProperty("购方银行,可选")
  private String buyerBank;

  /**
   * 购方银行账户
   */
  @ApiModelProperty("购方银行账户,可选")
  private String buyerBankAccount;

  /**
   * 购方邮箱
   */
  @ApiModelProperty("购方邮箱,可选")
  private String buyerEmail;

  /**
   * 购方手机号
   */
  @ApiModelProperty("购方手机号,可选")
  private String buyerMobile;

  /**
   * 显示购方银行账户 SHOW-显示 HIDE-不显示
   */
  @ApiModelProperty("发票是否显示购方银行账户 SHOW-显示 HIDE-不显示,必填默认HIDE")
  private InvoiceShowBuyerBankAccountEnum showBuyerBankAccount;

  @ApiModelProperty("发票开具金额")
  private InvoiceAmountDTO invoiceAmount;


  public void valid() {
    if (Objects.nonNull(this.businessType) && InvoiceBusinessTypeEnum.B2B.equals(
        this.businessType)) {
      throw new RuntimeException("不支持B2B开票");
    }
  }


}
