package com.yxt.invoice.application.service.impl.exists_invoice_query_third;

import com.yxt.domain.order.order_query.req.B2cOrderPageSearchReq;
import com.yxt.domain.order.order_query.res.B2cOrderSimpleRes;
import com.yxt.invoice.application.third.order.feign.B2COrderQueryDomainApiFeign;
import com.yxt.invoice.domain.command.OrderExistsInvoiceQueryCommand;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.types.invoice.enums.InvoiceBusinessTypeEnum;
import com.yxt.order.types.offline.OfflineThirdOrderNo;
import java.util.Collections;
import javax.annotation.Resource;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Component
@Order(3)
public class ExistInvoiceB2CThird extends AbstractExistsInvoiceQueryThird{

  @Resource
  private B2COrderQueryDomainApiFeign b2COrderQueryDomainApiFeign;

  @Override
  public OrderExistsInvoiceQueryCommand build(String thirdOrderNo) {
    OrderExistsInvoiceQueryCommand queryCommand = null;

    B2cOrderPageSearchReq searchParam = new B2cOrderPageSearchReq();
    searchParam.setThirdOrderNos(Collections.singletonList(thirdOrderNo));
    ResponseBase<PageDTO<B2cOrderSimpleRes>> pageDTOResponseBase = b2COrderQueryDomainApiFeign.orderSearchPage(
        searchParam);
    if (pageDTOResponseBase.checkSuccess() && null != pageDTOResponseBase.getData()
        && !pageDTOResponseBase.getData().getData().isEmpty()) {
      B2cOrderSimpleRes b2cOrderSimpleRes = pageDTOResponseBase.getData().getData().get(0);
      queryCommand = new OrderExistsInvoiceQueryCommand();
      queryCommand.setOrderNo(String.valueOf(b2cOrderSimpleRes.getOmsOrderNo())); // 设置omsOrderNo
      queryCommand.setBusinessType(InvoiceBusinessTypeEnum.B2C);
      queryCommand.setThirdPlatformCode(b2cOrderSimpleRes.getThirdPlatformCode());
      queryCommand.setThirdOrderNo(
          OfflineThirdOrderNo.thirdOrderNo(b2cOrderSimpleRes.getThirdOrderNo()));
      queryCommand.setStoreCode(b2cOrderSimpleRes.getOnlineStoreCode());
    }

    return queryCommand;
  }
}
