package com.yxt.invoice.infrastructure.db.mysql.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import lombok.Data;

import java.util.Date;

/**
 * 发票抬头数据库实体
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-06
 */
@Data
@TableName("invoice_title")
public class InvoiceTitleDO {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 抬头内部单号
     */
    private String invoiceTitleNo;

    /**
     * 会员编号
     */
    private String userId;

    /**
     * 发票类型 专票:SPECIAL_INVOICE 普票:ORDINARY_INVOICE
     */
    private String invoiceType;

    /**
     * 购方类型 个人-INDIVIDUAL 单位-ORGANIZATION
     */
    private String buyerPartyType;

    /**
     * 购方名字
     */
    private String buyerName;

    /**
     * 购方个人身份证单位纳税人识别号
     */
    private String buyerTin;

    /**
     * 购方电话
     */
    private String buyerPhone;

    /**
     * 购方地址
     */
    private String buyerAddress;

    /**
     * 购方银行,专票必填
     */
    private String buyerBank;

    /**
     * 购方银行账户,专票必填
     */
    private String buyerBankAccount;

    /**
     * 购方邮箱
     */
    private String buyerEmail;

    /**
     * 购方手机号
     */
    private String buyerMobile;

    /**
     * 是否起效 1-起效 -1-未起效
     */
    private Long isValid;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 系统创建时间
     */
    private Date sysCreateTime;

    /**
     * 系统更新时间
     */
    private Date sysUpdateTime;

    /**
     * 数据版本，每次update+1
     */
    @Version
    private Long version;
}
