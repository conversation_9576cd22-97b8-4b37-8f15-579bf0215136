package com.yxt.invoice.application.service.impl.exists_invoice_query_third;

import com.yxt.domain.order.order_query.req.OrderPageSearchReq;
import com.yxt.domain.order.order_query.res.OrderSimpleRes;
import com.yxt.invoice.application.third.order.feign.OrderQueryDomainApiFeign;
import com.yxt.invoice.domain.command.OrderExistsInvoiceQueryCommand;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.types.invoice.enums.InvoiceBusinessTypeEnum;
import com.yxt.order.types.offline.OfflineThirdOrderNo;
import com.yxt.order.types.order.enums.OrderServiceModeEnum;
import java.util.Collections;
import javax.annotation.Resource;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Component
@Order(2)
public class ExistInvoiceO2OThird extends AbstractExistsInvoiceQueryThird {

  @Resource
  private OrderQueryDomainApiFeign orderQueryDomainApiFeign;

  @Override
  public OrderExistsInvoiceQueryCommand build(String thirdOrderNo) {
    OrderExistsInvoiceQueryCommand queryCommand = null;

    OrderPageSearchReq searchParam = new OrderPageSearchReq();
    searchParam.setThirdOrderNoList(Collections.singletonList(thirdOrderNo));
    ResponseBase<PageDTO<OrderSimpleRes>> pageDTOResponseBase = orderQueryDomainApiFeign.orderSearchPage(
        searchParam);
    if (pageDTOResponseBase.checkSuccess() && null != pageDTOResponseBase.getData()
        && !pageDTOResponseBase.getData().getData().isEmpty()) {
      OrderSimpleRes simpleRes = pageDTOResponseBase.getData().getData().get(0);
      if (OrderServiceModeEnum.O2O.name().equals(simpleRes.getServiceMode())) {
        queryCommand = new OrderExistsInvoiceQueryCommand();
        queryCommand.setOrderNo(simpleRes.getOrderNo());
        queryCommand.setBusinessType(InvoiceBusinessTypeEnum.O2O);
        queryCommand.setThirdPlatformCode(simpleRes.getThirdPlatformCode());
        queryCommand.setThirdOrderNo(OfflineThirdOrderNo.thirdOrderNo(simpleRes.getThirdOrderNo()));
        queryCommand.setStoreCode(simpleRes.getOrganizationCode());
      }
    }

    return queryCommand;
  }
}
